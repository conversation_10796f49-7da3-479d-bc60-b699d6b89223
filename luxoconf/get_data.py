import pyodbc
import csv

def table_2_csv(c, table_name):
    with open(table_name+'.csv', 'w') as f:
        writer = csv.writer(f)
        c.execute (f'select * from {table_name}')
        writer.writerow([fd[0] for fd in c.description])
        for row in c.fetchall():
            writer.writerow(row)


con = pyodbc.connect(
    'DRIVER={ODBC Driver 18 for SQL Server};SERVER=***********;DATABASE=gpt;UID=sa;PWD=jfk;TrustServerCertificate=yes',
    unicode_results=True
)
c = con.cursor()

for table in ['feriado', 'linha', 'encomenda', 'job', 'job_mov']:
    table_2_csv(c, table)

con.close()
