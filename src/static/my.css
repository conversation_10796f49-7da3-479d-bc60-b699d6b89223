body::-webkit-scrollbar {
    display: none;
  }

.pure-g > div {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.l-box {
    padding: 0.5em;
}
.footer {
    background-color: #f3f3f3;
    border-top: 1px solid #eee;
    padding: 3px;
    margin-top: 10px;
    /*position: fixed;
    bottom: 0;*/
    width: 100%;
}
a {
    color: #777;
}
/*a:hover {
    background-color: #777;
    color: white;
}*/

/*
form p {
    margin-top: 0.1em;
}
label {
    display: inline-block;
    width: 120px;
}
*/
form p {
    margin: 0.5em 0em;
}
form label {
    display: block;
    font-weight: bold;
    /*font-size: 85%;*/
}
input[type=text],
input[type=password],
input[type=number],
input[type=date],
input[type=datetime-local]
{
    border: 1px solid rgb(180,180,180);
    width: 100%;
    padding: 3px;
    box-sizing: border-box;

}

textarea 
{
    border: 1px solid rgb(180,180,180);
    width: 100%;
    padding: 3px;
    box-sizing: border-box;

}

input[type=submit] {
    /*font-size: 85%;*/
    width: 100%;
}


.helptext {
    font-size: 75%;
    display: block;
}
.errorlist {
    list-style: none;
    background-color: rgb(238, 87, 87);
    color: white;
    font-size: 85%;
    margin-bottom: 0px;
    /*margin-left: 120px;*/
    padding: 2px 5px;
    border-radius: 2px;
}
div.foto-wrapper {  
    float:left; /* important */  
    position:relative; /* important(so we can absolutely position the description div */  
    margin: 0 10px 10px 0;
}  
div.foto-description {  
    position:absolute; /* absolute position (so we can position it where we want)*/  
    bottom:0px; /* position will be on bottom */  
    left:0px;  
    width:100%;  
    /* styling bellow */  
    background-color:black;  
    font-family: 'tahoma';  
    font-size:75%;  
    color:white;  
    opacity:0.6; /* transparency */  
    filter:alpha(opacity=60); /* IE transparency */  
}  
.foto-description p { 
    padding-left: 3px;  
    margin: 0px;  
} 
.timetable {
    font-size: 75%;
    border: 1px solid white;
    width: 100%;
}
.timetable th {
    font-weight: bold;
    background-color: #eee;
    border: 1px solid white;
    padding: 2px;
}

.timetable .sessao,
.timetable .grupo {
    color: white;
    background-color: #5eb95e;
    border: 1px solid white;
    text-align: center;
    font-weight: bold;
    padding: 2px;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

.pmtable {
    font-size: 80%;
    width: 100%;
}
.pmtable tr{
    border-bottom: 1px solid #aaa;
}
.pmtable th{
    background-color: gray;
    color: white;
}
.pmtable td,
.pmtable th {
    padding: 3px;
    text-align: left;
}
.pmtable-hover tr:hover{
    background-color: #eee;
}
.time {
    background-color: #5eb95e;
    color:white;
    padding: 1px;
    border-radius: 5px;
}
.sublabel {
    background-color:gray; 
    color:white; 
    border-radius:4px;
    padding:0px 3px;
}

.pagination {
    font-size: 80%;
    margin-top: 5px;
}

.date {
    background-color: rgb(210, 224, 210);
}

.money {
    text-align: right !important; 
}

.moneyalert{
    background-color: rgb(248, 100, 100  ); 
    color:white;
}


/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-container {
  position: relative;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 1001;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.modal-content {
  padding: 1rem;
  overflow-y: auto;
  flex-grow: 1;
}

.modal-footer {
  padding: 1rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Document viewer styles */
.document-viewer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.document-details {
  background-color: #f5f5f5;
  padding: 0.5rem;
  border-radius: 4px;
}

.qr-info pre {
  white-space: pre-wrap;
  font-size: 0.8rem;
  max-height: 150px;
  overflow-y: auto;
}

.document-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.doc-thumbnail {
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
}

.download-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #f0f0f0;
  border-radius: 4px;
  text-decoration: none;
  color: #333;
}

/* Justification cell styles */
.justificacao-cell {
  position: relative;
}

.doc-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.5rem;
  width: 24px;
  height: 24px;
  background-color: #f0f0f0;
  border-radius: 50%;
  cursor: pointer;
  color: #555;
}

.doc-link:hover {
  background-color: #e0e0e0;
  color: #000;
}