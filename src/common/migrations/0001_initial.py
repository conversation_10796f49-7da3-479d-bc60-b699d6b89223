# Generated by Django 5.0.2 on 2024-02-16 15:32

import django.contrib.postgres.indexes
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Pais',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('codigo', models.CharField(max_length=2)),
                ('nome', models.CharField(max_length=100)),
            ],
            options={
                'ordering': ('nome',),
            },
        ),
        migrations.CreateModel(
            name='DCF',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('codigo', models.Char<PERSON>ield(max_length=6, unique=True)),
                ('tipo', models.CharField(choices=[('D', 'Distrito'), ('C', 'Concel<PERSON>'), ('F', 'Freguesia')], max_length=1)),
                ('nome', models.CharField(max_length=150)),
            ],
            options={
                'verbose_name': 'Distrito/Concelho/Freguesia',
                'ordering': ['tipo', 'nome'],
                'indexes': [django.contrib.postgres.indexes.GinIndex(fields=['nome'], name='common_dcf_gin_idx', opclasses=('gin_trgm_ops',))],
            },
        ),
        migrations.CreateModel(
            name='CPostal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=50)),
                ('rua', models.CharField(max_length=120)),
                ('concelho', models.ForeignKey(limit_choices_to={'tipo': 'C'}, null=True, on_delete=django.db.models.deletion.PROTECT, to='common.dcf')),
            ],
            options={
                'verbose_name': 'Código Postal',
                'ordering': ['nome', 'rua'],
                'indexes': [django.contrib.postgres.indexes.GinIndex(fields=['nome', 'rua'], name='common_cpostal_gin_idx', opclasses=('gin_trgm_ops', 'gin_trgm_ops'))],
            },
        ),
        migrations.CreateModel(
            name='Profissao',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('codigo', models.CharField(max_length=10, unique=True)),
                ('nome', models.CharField(max_length=180)),
            ],
            options={
                'verbose_name': 'Profissão',
                'verbose_name_plural': 'Profissões',
                'ordering': ['nome'],
                'indexes': [django.contrib.postgres.indexes.GinIndex(fields=['nome'], name='common_profissao_gin_idx', opclasses=('gin_trgm_ops',))],
            },
        ),
    ]
