from django.db import models
from django.db.models import Q
from django.contrib.postgres.indexes import GinIndex

from .constants import DCF_TIPO, DISTRITO, CONCELHO, FREGUESIA


#
#  M A N A G E R S
#

class PaisManager(models.Manager):
    def lookup(self, request, q):
        qs = self.all()
        if q:
            qs = qs.filter(nome__icontains=q)
        return qs


class DCFManager(models.Manager):
    def distritos(self):
        return self.filter(tipo=DISTRITO)

    def concelhos(self):
        return self.filter(tipo=CONCELHO)

    def freguesias(self):
        return self.filter(tipo=FREGUESIA)

    def lookup(self, request, q:str):
        qs = self.freguesias()
        if q:
            qs = qs.filter(nome__icontains=q)
        return qs  
    

class CPostalManager(models.Manager):
    def lookup(self, request, q):
        qs = self.all()
        if q:
            if q[0].isdigit():
                qs = qs.filter(nome__startswith=q)
            else:
                qs = qs.filter(Q(nome__icontains=q) | Q(rua__icontains=q))
        return qs        


class ProfissaoManager(models.Manager):
    def lookup(self, request, q):
        qs = self.all()
        if q:
            if q.isdigit():
                qs = qs.filter(codigo__startswith=q).order_by('codigo')
            else:
                qs = qs.filter(nome__icontains=q)
        return qs

#
#  M O D E L S
#

class Pais(models.Model):
    objects = PaisManager()

    codigo = models.CharField(max_length=2)
    nome = models.CharField(max_length=100)

    class Meta:
        ordering = ('nome',)

    def __str__(self):
        return f'{self.nome} ({self.codigo})'
    

class DCF(models.Model):
    objects = DCFManager()
    name_cache = {}

    codigo = models.CharField(max_length=6, unique=True)
    tipo = models.CharField(max_length=1, choices=DCF_TIPO)
    nome = models.CharField(max_length=150)

    class Meta:
        ordering = ['tipo', 'nome']
        verbose_name = 'Distrito/Concelho/Freguesia'
        indexes = (
            GinIndex(name='common_dcf_gin_idx',fields=('nome',), opclasses=('gin_trgm_ops',)),
        )

    # @lru_cache
    def __str__(self):
        return f'{self.nome}'
        if self.tipo == DISTRITO:
            return f'{self.nome}'
        if self.tipo == CONCELHO:
            return f'{self.nome} / {DCF.objects.get(codigo=self.codigo[:2])}'
        if self.tipo == FREGUESIA:
            return f'{self.nome} / {DCF.objects.get(codigo=self.codigo[:4])}'


class CPostal(models.Model):
    objects = CPostalManager()

    nome = models.CharField(max_length=50)
    rua = models.CharField(max_length=120)
    concelho = models.ForeignKey(DCF, on_delete=models.PROTECT, null=True, 
        limit_choices_to={'tipo': CONCELHO})
    latitude = models.DecimalField(max_digits=10, decimal_places=6, null=True)
    longitude = models.DecimalField(max_digits=10, decimal_places=6, null=True)

    class Meta:
        ordering = ['nome', 'rua']
        verbose_name = 'Código Postal'
        indexes = (
            GinIndex(name='common_cpostal_gin_idx', fields=('nome', 'rua'), 
                opclasses=('gin_trgm_ops',"gin_trgm_ops")
            ),
        )

    def __str__(self):
        return u'{} ({})'.format(self.nome, self.rua)


class Profissao(models.Model):
    objects = ProfissaoManager()

    codigo = models.CharField(max_length=10, unique=True)
    nome = models.CharField(max_length=180)

    class Meta:
        ordering = ['nome']
        verbose_name = 'Profissão'
        verbose_name_plural = 'Profissões'
        indexes = (
            GinIndex(name='common_profissao_gin_idx', fields=('nome',), opclasses=('gin_trgm_ops',)),
        )

    def __str__(self):
        return f'{self.nome} ({self.codigo})'
