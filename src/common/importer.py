from core.importer import SQLImporter


class Paises(SQLImporter):
    filename = 'paises.csv'
    fields = 'codigo;nome'
    skip_first_line = True


class Distritos(SQLImporter):
    filename = 'distritos.csv'    
    fields = 'nivel,dicofre,designacao,bandeira,wikipedia'
    skip_first_line = True


class Concelhos(SQLImporter):
    filename = 'concelhos.csv'    
    fields = 'nivel,dicofre,designacao,wiki_image_large,codigo,wikipedia,brasao'
    skip_first_line = True


class Freguesias(SQLImporter):
    filename = 'freguesias.csv'            
    fields = 'nivel,distrito,concelho,freguesia,dicofre,brasao'
    skip_first_line = True


class Profissoes(SQLImporter):
    filename = 'INE_CPP2010.csv'
    fields = 'nivel;codigo;designacao'
    skip_first_line = True


class CPostalCPS(SQLImporter):
    filename = 'todos_cp.txt'
    encoding = 'latin-1'
    fields = 'distrito_cod;concelho_cod;localidade_cod;localidade_nome;arteria_cod;'\
        'arteria_tipo;preposicao1;arteria_titulo;preposicao2;arteria_nome;'\
        'arteria_local;troco;porta;cliente;cp4;cp3;cpnome'
    skip_first_line = False


class CpostalLatLon(SQLImporter):
    filename = 'cp_lat_lon.csv'            
    fields = 'cp,lat,lon'
    skip_first_line = True


class CPostalAPS(SQLImporter):
    filename = 'todos_aps.txt'
    encoding = 'latin-1'
    fields = 'ep_nome;apa_ini;apa_fim;cp4_ap;cp3_ap;cpalf_ap;cp4_apc;cp3_apc;cpalf_apc'
    skip_first_line = False

    def after_import(self):
        self.runsql('''
            insert into common_pais(codigo,nome) select codigo,nome from tmp_paises;
            insert into common_dcf(codigo,tipo,nome)
            select dicofre,'D',designacao from tmp_distritos;
            insert into common_dcf(codigo,tipo,nome)
            select dicofre,'C',designacao from tmp_concelhos;
            insert into common_dcf(codigo,tipo,nome)
            select dicofre,'F',freguesia from tmp_freguesias;
            insert into common_profissao(codigo,nome)
            select codigo,designacao from tmp_INE_CPP2010;
            with cpostal as (
            SELECT
                    concat(cp4, '-', cp3, ' ',cpnome) nome,
                    concat(
                        concat_ws(' ', 
                            arteria_tipo, preposicao1, arteria_titulo, preposicao2, arteria_nome,
                            case when arteria_nome is null then COALESCE(cliente,localidade_nome) else null end,
                            case when troco is not null or porta is not null then concat('(',troco,porta,')') else null end
                        ),
                        case when arteria_local is not null then concat(', ', arteria_local) else null end
                    ) rua,
                    concat(distrito_cod,concelho_cod) concelho_codigo
                from tmp_todos_cp
                union all
                select case 
                    when apa_fim is null then concat(cp4_apc,'-',cp3_apc,' ',cpalf_apc) 
                    else concat(cp4_ap,'-',cp3_ap,' ',cpalf_ap) end nome,
                    case
                    when apa_fim is null then concat(apa_ini,' ',ep_nome) 
                    else concat(apa_ini,'-',apa_fim,' ',ep_nome) end rua,
                    null concelho_codigo
                from tmp_todos_aps
            )
            insert into common_cpostal(nome, rua, concelho_id)
            select cpostal.nome, trim(cpostal.rua), common_dcf.id
            from cpostal
            left join common_dcf on cpostal.concelho_codigo=common_dcf.codigo;
            update common_cpostal
            set latitude=lat::numeric(10,6), longitude=lon::numeric(10,6)
            from tmp_cp_lat_lon
            where substring(nome,1,8) = tmp_cp_lat_lon.cp;
        ''')

