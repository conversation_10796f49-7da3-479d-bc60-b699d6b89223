from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from common.models import CPostal
import requests
from bs4 import BeautifulSoup


cached_lat_lon = {}

def get_lat_lon(cp4, cp3):
    lat = lon = 0
    if (cp4, cp3) in cached_lat_lon:
        return cached_lat_lon[(cp4, cp3)]
    r = requests.get('http://www.codigo-postal.pt', params={'cp4': cp4, 'cp3': cp3})
    # print(r.status_code)
    if r.status_code == 200:
        try:
            soup = BeautifulSoup(r.text, 'html.parser')
            el = soup.find('span', class_='gps')
            s = list(el.strings)[-1]
            lat, lon = s.strip().split(',')
            cached_lat_lon[(cp4, cp3)] = (lat, lon)
            # print('inside lat lon',lat, lon)
        except:
            pass
    return lat, lon



class Command(BaseCommand):
    help = "Fill in missing gps coordinates for codigo postal table"

    def handle(self, *args, **options):
        objs = CPostal.objects.filter(latitude__isnull=True)
        for cp in objs:
            print('processing', cp, end='')
            cp4, cp3 = cp.nome[:8].split('-')
            lat, lon = get_lat_lon(cp4, cp3)
            print(lat, lon)
            cp.latitude = lat
            cp.longitude = lon
            cp.save()
