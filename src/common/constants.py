

DISTRITO, CONCELHO, FREGUESIA = 'D', 'C', 'F'
DCF_TIPO = {
    DISTRITO: 'Distrito',
    CONCELHO: 'Concel<PERSON>',
    FREGUESIA: 'Freguesia',
}


BI, CP, PP, AR, CC, O = 1, 2, 3, 4, 5, 9
DI_TIPO = {
    BI: 'Bilhete de Identidade',
    CP: 'Cédula Pessoal',
    PP: 'Passaporte',
    AR: 'Autorização de Residência',
    CC: 'Cartão do Cidadão',
    O: 'Outro'
}

SEXO = {
    'M': 'Masculino',
    'F': 'Feminino'
}


ESTADO_CIVIL = {
    1: 'Solte<PERSON>',
    2: 'União de fato',
    3: 'Casado',
    4: 'Separado',
    5: 'Divorciado',
    6: 'Viuvo',
    9: 'Outro'
}


FORMACAO_ACADEMICA = {
    1: 'Licenciatura',
    2: 'Bacharel<PERSON>',
    3: 'Pós-graduação',
    4: 'Mestra<PERSON>',
    5: 'Doutoramento',
    6: 'Diploma de Estudos Superiores Especializados',
    7: 'Magistério Primário/Educadores de Infância',
    8: 'Secundário',
    9: 'Básico (3.º ciclo)',
    10: 'Básico (2.º ciclo)',
    11: 'Básico (1.º ciclo)',
    12: 'Sem Habilitação',
    99: 'Outra'
}


SITUACAO_EMPREGO = {
    1: 'Trabalhador por conta de outrem',
    2: 'Trabalhador por conta própria como isolado',
    3: 'Trabalhador por conta própria como empregador',
    4: 'Desempregado',
    5: 'Estudante',
    6: 'Doméstico',
    7: 'Reformado',
    8: 'Situação Desconhecida',
    9: 'Outra',
}


UNIVERSIDADE_TIPO = {
    1: 'Ensino Superior Público Universitário',
    2: 'Ensino Superior Público Politécnico',
    3: 'Ensino Superior Particular e Cooperativo Universitário',
    4: 'Ensino Superior Particular e Cooperativo Politécnico',
    5: 'Ensino Superior Público Militar e Policial',
    6: 'Ensino Não Superior',
}
