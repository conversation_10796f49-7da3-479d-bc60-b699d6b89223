from django.contrib import admin

from .models import Empresa, DocNumber


@admin.register(Empresa)
class EmpresaAdmin(admin.ModelAdmin):
    list_display = ['nome_abrev', 'nome', 'nif', 'iban', 'bic']


@admin.register(DocNumber)
class DocNumberAdmin(admin.ModelAdmin):
    list_display = ['empresa_nome_abrev', 'doc', 'ano', 'numero']
    list_select_related = ['empresa']

    def empresa_nome_abrev(self, obj):
        return obj.empresa.nome_abrev
