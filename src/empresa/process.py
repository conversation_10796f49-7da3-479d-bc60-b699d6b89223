from datetime import datetime
from decimal import Decimal
from pathlib import Path

from .models import Empresa, BancoMovimento


def bancomovimento_import_csv(empresa: Empresa, file_path: Path):
    'Importação do ficheiro do Montepio'
    with file_path.open('r', encoding='windows 1252') as file:
        in_table = False
        data_old = 0
        data_ix = 0
        movements = []        
        for line in file:
            line = line.strip()
            if not line:
                continue
            if not in_table:
                if line.startswith('DATA MOV.\tDATA VALOR\tDESCRIÇÃO\tIMPORTÂNCIA'):
                    in_table = True
            else:
                cols = line.split('\t')
                if cols[0] != data_old:
                    data_old = cols[0]
                    data_ix = 1
                else:
                    data_ix += 1
                try:
                    mov = BancoMovimento(
                        empresa=empresa,
                        data=datetime.strptime(cols[0], '%Y-%m-%d').date(),
                        data_ix=data_ix,
                        data_valor=datetime.strptime(cols[1], '%Y-%m-%d').date(),
                        descricao=cols[2],
                        valor=Decimal(cols[3].replace('.','').replace(',','.')),
                        saldo_contabilistico=Decimal(cols[4].replace('.','').replace(',','.'))
                    )
                    movements.append(mov)
                except Exception as e:
                    print(f"Error processing line {line}: {e}")
        
        # Use bulk_create with ignore_conflicts to handle duplicates
        BancoMovimento.objects.bulk_create(movements, ignore_conflicts=True)