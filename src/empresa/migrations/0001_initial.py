# Generated by Django 4.1.5 on 2023-04-05 08:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Empresa',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=100)),
                ('nome_abrev', models.CharField(max_length=20)),
                ('morada', models.CharField(max_length=100)),
                ('cpostal', models.Char<PERSON>ield(max_length=100)),
                ('nif', models.<PERSON>r<PERSON><PERSON>(max_length=15)),
                ('iban', models.Char<PERSON>ield(max_length=27)),
                ('bic', models.Char<PERSON>ield(max_length=15)),
            ],
        ),
        migrations.CreateModel(
            name='DocNumber',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('doc', models.Char<PERSON>ield(max_length=30)),
                ('ano', models.IntegerField()),
                ('numero', models.IntegerField()),
                ('empresa', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='empresa.empresa')),
            ],
            options={
                'unique_together': {('empresa', 'doc', 'ano')},
            },
        ),
    ]
