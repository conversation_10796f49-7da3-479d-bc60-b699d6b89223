# Generated by Django 5.0.6 on 2024-07-08 14:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('empresa', '0004_bancomovimento_content_type_bancomovimento_object_id'),
        ('terceiro', '0002_alter_terceiro_tipo'),
    ]

    operations = [
        migrations.CreateModel(
            name='TIB',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.DateTimeField()),
                ('tipo', models.CharField(choices=[('01', 'Água'), ('02', 'Gás'), ('03', 'Electricidade'), ('04', 'Gás/Electricidade'), ('05', 'Telefone'), ('06', 'Telex'), ('06', 'Renda da casa'), ('08', 'Ordenados'), ('09', 'Fornecedores'), ('12', 'Transferência')], default='12', max_length=2)),
                ('nome', models.CharField(blank=True, max_length=100)),
                ('empresa', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='empresa.empresa')),
            ],
            options={
                'ordering': ['-data'],
            },
        ),
        migrations.CreateModel(
            name='TIBLinha',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('valor', models.DecimalField(decimal_places=2, max_digits=10)),
                ('terceiro', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='terceiro.terceiro')),
                ('tib', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='empresa.tib')),
            ],
            options={
                'unique_together': {('tib', 'terceiro')},
            },
        ),
    ]
