# Generated by Django 5.0.6 on 2024-06-28 08:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('empresa', '0002_alter_empresa_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='BancoMovimento',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.DateField()),
                ('data_ix', models.IntegerField()),
                ('data_valor', models.DateField()),
                ('descricao', models.CharField(max_length=100)),
                ('valor', models.DecimalField(decimal_places=2, max_digits=10)),
                ('saldo_contabilistico', models.DecimalField(decimal_places=2, max_digits=10)),
                ('justificacao', models.CharField(blank=True, max_length=100)),
                ('empresa', models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, to='empresa.empresa')),
            ],
            options={
                'ordering': ['data', 'data_ix'],
                'unique_together': {('empresa', 'data', 'data_ix')},
            },
        ),
    ]
