# Generated by Django 5.1.4 on 2025-06-25 07:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('docs', '0001_initial'),
        ('empresa', '0005_tib_tiblinha'),
    ]

    operations = [
        migrations.CreateModel(
            name='BancoExtrato',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ano', models.IntegerField(help_text='Year of the bank statement')),
                ('mes', models.IntegerField(help_text='Month of the bank statement (1-12)')),
                ('data_upload', models.DateTimeField(auto_now_add=True)),
                ('observacoes', models.TextField(blank=True, help_text='Additional notes about this statement')),
                ('documento', models.ForeignKey(help_text='Bank statement document from docs app', on_delete=django.db.models.deletion.PROTECT, to='docs.doc')),
                ('empresa', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='empresa.empresa')),
            ],
            options={
                'verbose_name': 'Bank Statement',
                'verbose_name_plural': 'Bank Statements',
                'ordering': ['-ano', '-mes'],
                'unique_together': {('empresa', 'ano', 'mes')},
            },
        ),
    ]
