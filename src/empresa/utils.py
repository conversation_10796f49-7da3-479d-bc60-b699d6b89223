from dataclasses import dataclass
from datetime import datetime, date
from decimal import Decimal   
from typing import get_type_hints


@dataclass
class ETCodeQR:
    nif_do_emitente: str=''
    nif_do_adquirente: str=''
    pais_do_adquirente: str=''
    tipo_de_documento: str=''
    estado_do_documento: str=''
    data_do_documento: date=None
    id_do_documento: str=''
    ATCUD: str=''
    espaco_fiscal: str=''
    base_tributavel_isenta: Decimal=None
    base_tributavel_taxa_reduzida: Decimal=None
    total_iva_taxa_reduzida: Decimal=None
    base_tributavel_taxa_intermedia: Decimal=None
    total_iva_taxa_intermedia: Decimal=None
    base_tributavel_taxa_normal: Decimal=None
    total_iva_taxa_normal: Decimal=None
    espaco_fiscal_2: str=''
    base_tributavel_isenta_2: Decimal=None
    base_tributavel_taxa_reduzida_2: Decimal=None
    total_iva_taxa_reduzida_2: Decimal=None
    base_tributavel_taxa_intermedia_2: Decimal=None
    total_iva_taxa_intermedia_2: Decimal=None
    base_tributavel_taxa_normal_2: Decimal=None
    total_iva_taxa_normal_2: Decimal=None
    espaco_fiscal_3: str=''
    base_tributavel_isenta_3: Decimal=None
    base_tributavel_taxa_reduzida_3: Decimal=None
    total_iva_taxa_reduzida_3: Decimal=None
    base_tributavel_taxa_intermedia_3: Decimal=None
    total_iva_taxa_intermedia_3: Decimal=None
    base_tributavel_taxa_normal_3: Decimal=None
    total_iva_taxa_normal_3: Decimal=None
    total_nao_sujeito: Decimal=None
    imposto_do_selo: Decimal=None
    total_impostos: Decimal=None
    total_do_documento: Decimal=None
    retencoes_na_fonte: Decimal=None
    hash: str=''
    num_certificado: str=''
    outras_informacoes: str=''

    mapping = {
        'A': 'nif_do_emitente', 
        'B': 'nif_do_adquirente',
        'C': 'pais_do_adquirente',
        'D': 'tipo_de_documento',
        'E': 'estado_do_documento',
        'F': 'data_do_documento',
        'G': 'id_do_documento',
        'H': 'ATCUD',
        'I1': 'espaco_fiscal',
        'I2': 'base_tributavel_isenta',
        'I3': 'base_tributavel_taxa_reduzida',
        'I4': 'total_iva_taxa_reduzida',
        'I5': 'base_tributavel_taxa_intermedia',
        'I6': 'total_iva_taxa_intermedia',
        'I7': 'base_tributavel_taxa_normal',
        'I8': 'total_iva_taxa_normal',
        'J1': 'espaco_fiscal_2',
        'J2': 'base_tributavel_isenta_2',
        'J3': 'base_tributavel_taxa_reduzida_2',
        'J4': 'total_iva_taxa_reduzida_2',
        'J5': 'base_tributavel_taxa_intermedia_2',
        'J6': 'total_iva_taxa_intermedia_2',
        'J7': 'base_tributavel_taxa_normal_2',
        'J8': 'total_iva_taxa_normal_2',
        'K1': 'espaco_fiscal_3',
        'K2': 'base_tributavel_isenta_3',
        'K3': 'base_tributavel_taxa_reduzida_3',
        'K4': 'total_iva_taxa_reduzida_3',
        'K5': 'base_tributavel_taxa_intermedia_3',
        'K6': 'total_iva_taxa_intermedia_3',
        'K7': 'base_tributavel_taxa_normal_3',
        'K8': 'total_iva_taxa_normal_3',
        'L': 'total_nao_sujeito',
        'M': 'imposto_do_selo',
        'N': 'total_impostos',
        'O': 'total_do_documento',
        'P': 'retencoes_na_fonte',
        'Q': 'hash',
        'R': 'num_certificado',
        'S': 'outras_informacoes'
    }    

    def __init__(self): # don't let dataclass generate init
        pass

    def __repr__(self) -> str:
        lst=[]
        for letter, field in ETCodeQR.mapping.items():
            v = getattr(self,field,None)
            if v:
                lst.append(f'{field}: {v}')
        return '\n'.join(lst)        

    @classmethod
    def from_qr(cls, serialized_str:str):
        obj = cls()
        type_hints = get_type_hints(cls)
        for item in serialized_str.split('*'):
            try:
                k, v = item.split(':')
                field = cls.mapping.get(k)
                if field:
                    field_type = type_hints[field]
                    if field_type is int:
                        vv = int(v)
                    elif field_type is date:
                        vv = datetime.strptime(v, '%Y%m%d').date()
                    elif field_type is Decimal:
                        vv = Decimal(v)
                    else:
                        vv = v
                    setattr(obj, field, vv)
            except:
                pass
        return obj
    
    def to_qr(self):
        lst = []
        type_hints = get_type_hints(ETCodeQR)
        for letter, field in ETCodeQR.mapping.items():
            v = getattr(self, field, None)
            if v:
                field_type = type_hints[field]
                if field_type is date:
                    vv = v.strftime('%Y%m%d')
                elif field_type is Decimal:
                    vv = f'{v:.2f}'
                else:
                    vv = v
                lst.append(f'{letter}:{vv}')
        return '*'.join(lst)
