from datetime import date

from django.db import models
from django.db.models import F
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.core.exceptions import ValidationError


#
#  M A N A G E R S
#

class EmpresaManager(models.Manager):
    pass


class DocNumberManager(models.Manager):
    def get_number_for_doc(self, empresa, doc, ano=None):
        if not ano:
            ano = date.today().year
        series = self.select_for_update().filter(empresa=empresa, doc=doc, ano=ano)
        if not series:
            try:
                self.create(empresa=empresa, doc=doc, ano=ano, numero=0)
                series = self.select_for_update().filter(empresa=empresa, doc=doc, ano=ano)
            except Exception:
                pass
        # series = self.select_for_update().filter(empresa=empresa, doc=doc, ano=ano)
        series.update(numero=F('numero') + 1)
        numero = self.get(empresa=empresa, doc=doc, ano=ano).numero
        return (ano, numero)


class BancoMovimentoManager(models.Manager):
    pass


class BancoExtratoManager(models.Manager):
    def get_for_month(self, empresa, ano, mes):
        """Get bank statement for a specific month and year"""
        return self.filter(empresa=empresa, ano=ano, mes=mes).first()

    def create_for_month(self, empresa, ano, mes, documento):
        """Create a new bank statement for a specific month"""
        # Check if statement already exists for this month
        existing = self.get_for_month(empresa, ano, mes)
        if existing:
            raise ValidationError(f"Bank statement already exists for {mes:02d}/{ano}")

        return self.create(
            empresa=empresa,
            ano=ano,
            mes=mes,
            documento=documento
        )


class TIBManager(models.Manager):
    pass


class TIBLinhaManager(models.Manager):
    pass


#
#  M O D E L S
#

class Empresa(models.Model):
    objects = EmpresaManager()

    nome = models.CharField(max_length=100, null=False, blank=False)
    nome_abrev = models.CharField(max_length=20, null=False, blank=False)
    nif = models.CharField(max_length=15, null=False, blank=False)
    morada = models.CharField(max_length=100, null=False, blank=False)
    cpostal = models.CharField(max_length=100, null=False, blank=False) 
    nif = models.CharField(max_length=15, null=False, blank=False) 
    iban = models.CharField(max_length=27, null=False, blank=False)
    bic = models.CharField(max_length=15, null=False, blank=False)

    class Meta:
        ordering = ['id']

    def __str__(self) -> str:
        return f'{self.nome_abrev} - {self.nome} {self.nif}'


class DocNumber(models.Model):
    objects = DocNumberManager()

    empresa = models.ForeignKey(Empresa, on_delete=models.CASCADE)
    doc = models.CharField(max_length=30)
    ano = models.IntegerField()
    numero = models.IntegerField()

    class Meta:
        unique_together = ('empresa', 'doc', 'ano')

    def __str__(self) -> str:
        return f'{self.empresa.nome_abrev}.{self.doc} - {self.ano} / {self.numero}'
    


class BancoMovimento(models.Model):
    objects = BancoMovimentoManager()

    empresa = models.ForeignKey(Empresa, on_delete=models.PROTECT, null=False, blank=False, default=1)
    data = models.DateField(null=False, blank=False)
    data_ix = models.IntegerField()  # index of mov for same data
    data_valor = models.DateField(null=False, blank=False)
    descricao = models.CharField(max_length=100, null=False, blank=False)
    valor = models.DecimalField(max_digits=10, decimal_places=2)
    saldo_contabilistico = models.DecimalField(max_digits=10, decimal_places=2)
    #condo stuff
    justificacao = models.CharField(max_length=100, null=False, blank=True)
    content_type = models.ForeignKey(ContentType, on_delete=models.SET_NULL, null=True)
    object_id = models.BigIntegerField(null=True)
    justificacao_doc = GenericForeignKey()

    class Meta:
        unique_together = ['empresa', 'data', 'data_ix']
        # unique_together = ['data', 'descricao', 'valor', 'saldo_contabilistico']
        ordering = ['data', 'data_ix']

    def __str__(self):
        return f'{self.data} {self.data_ix:>2} {self.descricao:40}  {self.valor:>10.2f} ({self.saldo_contabilistico:>10.2f})   # {self.justificacao}'

    def get_bank_statement(self):
        """Get the bank statement document for this movement's month"""
        return BancoExtrato.objects.get_for_month(
            empresa=self.empresa,
            ano=self.data.year,
            mes=self.data.month
        )


class BancoExtrato(models.Model):
    """Model to store monthly bank statements"""
    objects = BancoExtratoManager()

    empresa = models.ForeignKey(Empresa, on_delete=models.PROTECT, null=False, blank=False)
    ano = models.IntegerField(help_text="Year of the bank statement")
    mes = models.IntegerField(help_text="Month of the bank statement (1-12)")
    documento = models.ForeignKey('docs.Doc', on_delete=models.PROTECT,
                                help_text="Bank statement document from docs app")
    data_upload = models.DateTimeField(auto_now_add=True)
    observacoes = models.TextField(blank=True, help_text="Additional notes about this statement")

    class Meta:
        unique_together = ('empresa', 'ano', 'mes')
        ordering = ['-ano', '-mes']
        verbose_name = "Bank Statement"
        verbose_name_plural = "Bank Statements"

    def __str__(self):
        return f'{self.empresa.nome_abrev} - {self.mes:02d}/{self.ano} - {self.documento.name}'

    def clean(self):
        """Validate month is between 1-12"""
        if not (1 <= self.mes <= 12):
            raise ValidationError({'mes': 'Month must be between 1 and 12'})

    def get_movements(self):
        """Get all bank movements for this statement's month"""
        from datetime import date
        start_date = date(self.ano, self.mes, 1)
        if self.mes == 12:
            end_date = date(self.ano + 1, 1, 1)
        else:
            end_date = date(self.ano, self.mes + 1, 1)

        return BancoMovimento.objects.filter(
            empresa=self.empresa,
            data__gte=start_date,
            data__lt=end_date
        )

    @property
    def movements_count(self):
        """Count of movements in this statement's month"""
        return self.get_movements().count()


TIB_TIPO = (
    ('01', 'Água'),
    ('02', 'Gás'),
    ('03', 'Electricidade'),
    ('04', 'Gás/Electricidade'),
    ('05', 'Telefone'),
    ('06', 'Telex'),
    ('06', 'Renda da casa'),
    ('08', 'Ordenados'),
    ('09', 'Fornecedores'),
    ('12', 'Transferência'),
)


class TIB(models.Model):
    objects = TIBManager()

    empresa = models.ForeignKey(Empresa, on_delete=models.PROTECT)
    #conta = models.ForeignKey(ContaBancaria, on_delete=models.CASCADE)
    data  = models.DateTimeField()
    tipo  = models.CharField(max_length=2, default='12', choices=TIB_TIPO)
    nome  = models.CharField(max_length=100, blank=True)

    class Meta:
        ordering = ['-data']

    def __str__(self):
        return f'{self.nome} ({self.data:%Y%m%d})'

    def sepa(self):
        from .sepa import sepa_xml
        return sepa_xml(self.empresa, self.lines(), self.nome)


class TIBLinha(models.Model):
    objects = TIBLinhaManager()

    tib      = models.ForeignKey(TIB, on_delete=models.CASCADE, related_name='lines')
    terceiro = models.ForeignKey('terceiro.terceiro', on_delete=models.PROTECT)
    valor = models.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        unique_together = (('tib', 'terceiro'),)
