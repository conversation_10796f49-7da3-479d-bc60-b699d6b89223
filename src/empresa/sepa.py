from uuid import uuid4
from datetime import timedelta

from lxml import etree as ET
from lxml.builder import ElementMaker

from django.utils import timezone


def sepa_xml(empresa, lines, end_to_end_obs=''):
    # if not all([hasattr(empresa,attr) for attr in ['nome', 'nome_abrev', 'nif', 'morada', 'iban', 'bic']]):
    #     raise Exception('empresa object doesnt have the required attributes')
    # if not all([hasattr(lines[0],attr) for attr in ['total', 'terceiro']]):
    #     raise Exception('lines objects doesnt contain necessary attributes')

    nsmap = {
        None: "urn:iso:std:iso:20022:tech:xsd:pain.001.001.03",
        'xsi': "http://www.w3.org/2001/XMLSchema-instance"
    }

    tib_tipo_2_ct_code = {
        '08': 'SALA',
        '09': 'SUPP'
    }

    E = ElementMaker(nsmap=nsmap)

    lines_no = len(lines)
    lines_total = sum([line.total for line in lines])
    now = timezone.now()
    req_exec_date = now.date() + timedelta(days=4)
    msg_id = empresa.nome_abrev + '-' + str(uuid4())[:8]

    def lines_elements():
        elements = []
        for line in lines:
            le = E.CdtTrfTxInf(
                E.PmtId(
                    E.EndToEndId(end_to_end_obs)
                ),
                E.Amt(
                    E.InstdAmt(f'{line.total:.2f}', Ccy='EUR')
                ),
                E.CdtrAgt(
                    E.FinInstnId(
                        E.BIC(line.terceiro.bic)
                    )
                ),
                E.Cdtr(
                    E.Nm(line.terceiro.nome),
                    E.PstlAdr(
                        E.Ctry('PT'),
                        E.AdrLine(line.terceiro.morada)
                    ),
                ),
                E.CdtrAcct(
                    E.Id(
                        E.IBAN(line.terceiro.iban)
                    )
                )
            )
            elements.append(le)
        return elements

    doc = E.Document(
        E.CstmrCdtTrfInitn(
            E.GrpHdr(
                E.MsgId(msg_id),
                E.CreDtTm(f'{now:%Y-%m-%dT%H:%M:%S}'),
                E.NbOfTxs(str(lines_no)),
                E.CtrlSum(f'{lines_total:.2f}'),
                E.InitgPty(
                    E.Nm(empresa.nome),
                    E.Id(
                        E.PrvtId(
                            E.Othr(
                                E.Id(empresa.nif)
                            )
                        )
                    )
                )
            ),
            E.PmtInf(
                E.PmtInfId(msg_id),
                E.PmtMtd('TRF'),
                E.NbOfTxs(str(lines_no)),
                E.CtrlSum(f'{lines_total:.2f}'),
                E.PmtTpInf(
                    E.CtgyPurp(
                        E.Cd('SALA')
                    )
                ),
                E.ReqdExctnDt(f'{req_exec_date:%Y-%m-%d}'),
                E.Dbtr(
                    E.Nm(empresa.nome),
                    E.PstlAdr(
                        E.Ctry('PT'),
                        E.AdrLine(empresa.morada)
                    )
                ),
                E.DbtrAcct(
                    E.Id(
                        E.IBAN(empresa.iban)
                    )
                ),
                E.DbtrAgt(
                    E.FinInstnId(
                        E.BIC(empresa.bic)
                    )
                ),
                * lines_elements()
            )
        )
    )

    return ET.tostring(doc, pretty_print=True, xml_declaration=True, encoding='utf-8')
