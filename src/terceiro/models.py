from django.db import models
from django.core.exceptions import ValidationError

from core.dbfields import (
    NameField,AddressField, VATNOField, IBANField, BICField, 
    NISSField, PhoneField, ThumbnailImageField
)
from common.constants import SEXO, DI_TIPO, FORMACAO_ACADEMICA, SITUACAO_EMPREGO, CC
from core.validators import validate_citizen_card, validate_iban_bic
from core.validators.iban import get_bic_from_iban


#
#  M A N A G E R S
#

class TerceiroManager(models.Manager):
    def lookup(self, request, q):
        qs = self.all()
        if q:
            if q.isdigit():
                qs = qs.filter(nif__startswith=q)
            else:
                qs = qs.filter(nome__icontains=q)
        return qs


#
#  M O D E L S
#
    
class Terceiro(models.Model):
    objects = TerceiroManager()

    tipo             = models.CharField(max_length=1, blank=True)
    nome             = NameField()
    foto             = ThumbnailImageField(size=(200,240), upload_to='terceiro_%Y', blank=True, null=True)
    nacionalidade    = models.ForeignKey("common.Pais", blank=True, 
                        on_delete=models.SET_NULL, null=True, related_name='+')
    morada           = AddressField(blank=True)
    morada_dcf       = models.ForeignKey("common.DCF", blank=True,
                        on_delete=models.SET_NULL, null=True, related_name='+')
                        # limit_choices_to={'tipo': DCF_TIPO.FREGUESIA}, related_name='+')
    cpostal          = models.ForeignKey("common.CPostal", blank=True,
                        on_delete=models.SET_NULL, null=True, related_name='+')
    cpostal_alt      = models.CharField(max_length=50, blank=True)
    
    naturalidade     = models.ForeignKey("common.Pais", blank=True,
                        on_delete=models.SET_NULL, null=True, related_name='+')
    naturalidade_dcf = models.ForeignKey('common.DCF', blank=True,
                        on_delete=models.SET_NULL, null=True, related_name='+')

    nif      = VATNOField(unique=True, null=True)
    iban     = IBANField(blank=True)
    bic      = BICField(blank=True)
    niss     = NISSField(blank=True) 
    ncga     = models.CharField(max_length=10, blank=True)
    telefone = PhoneField(blank=True)
    fax      = PhoneField(blank=True)
    email    = models.EmailField(blank=True)
    www      = models.URLField(blank=True)
    obs      = models.TextField(blank=True)
    
    # documento de identificacao
    di_tipo         = models.IntegerField(blank=True, choices=DI_TIPO, null=True)
    di_numero       = models.CharField(max_length=30, blank=True)
    di_data_emissao  = models.DateField(blank=True, null=True)
    di_data_validade = models.DateField(blank=True, null=True)
    di_arquivo      = models.CharField(max_length=30, blank=True)
    data_nascimento = models.DateField(blank=True, null=True)
    sexo            = models.CharField(max_length=1, blank=True, choices=SEXO)

    # outros dados
    formacao_academica = models.IntegerField(choices=FORMACAO_ACADEMICA, blank=True, null=True)
    profissao = models.ForeignKey('common.Profissao', blank=True,
                        on_delete=models.SET_NULL, null=True, related_name='+')
    situacao_emprego = models.IntegerField(choices=SITUACAO_EMPREGO, blank=True, null=True)
    empresa_trabalha = models.CharField(max_length=100, blank=True)

    # OTHER
    empresa = models.ForeignKey('empresa.Empresa', on_delete=models.SET_NULL, null=True)
    user = models.OneToOneField('auth.User', on_delete=models.SET_NULL, null=True)
    
    ## data from winschool
    ws_escolaid = models.IntegerField(null=True, blank=True)
    ws_terceiroid =models.IntegerField(null=True, blank=True)
    ws_turma = models.CharField(max_length=30, null=True, blank=True)

    labels = {
        'cpostal': '. Código Postal',
        'cpostal_alt': '. Código Postal',
        'morada_dcf': '. Freguesia',
        'naturalidade_dcf': '. Freguesia',
        'nif': 'NIF',
        'iban': 'IBAN',
        'bic': 'BIC',
        'niss': 'NISS',
        'ncga': 'NCGA',
        'di_tipo': 'Documento de identificação',
        'di_numero':'. Numero',
        'di_data_emissao': '. Data emissão',
        'di_data_validade': '. Data validade',
        'di_arquivo': '. Arquivo',
        'formacao_academica': 'Formação académica',
        'profissao': 'Profissão',
        'situacao_emprego': 'Situação face ao emprego',
        'empresa_trabalha': 'Empresa onde trabalha',

    }
    help_texts = {
        'cpostal_alt': 'preencher apenas para <b>códigos postais internacionais</b>',
        'profissao': 'Escolha a profissão no catálogo nacional de profissões '\
            '<a href="https://www.ine.pt/xportal/xmain?xpid=INE&xpgid=ine_publicacoes&PUBLICACOESpub_boui=107961853&PUBLICACOESmodo=2&xlang=pt" target="_blank">CPP2010</a>',
    }

    class Meta:
        ordering = ('nome',)

    def __str__(self) -> str:
        #return f'{self.nome:60} ({self.nif if self.nif else '':10})'
        return f'{self.nome} ({self.nif})'

    def clean(self):
        errors = {}
        if self.di_tipo == CC:
            try:
                validate_citizen_card(self.di_numero)
            except ValidationError as ve:
                errors['di_numero'] = ve
        if self.iban: # filled iban
            if self.bic:
                try:
                    validate_iban_bic(self.iban, self.bic)
                except ValidationError as ve:
                    errors['bic'] = ve
            else:
                self.bic = get_bic_from_iban(self.iban)
        if errors:
            raise ValidationError(errors)
