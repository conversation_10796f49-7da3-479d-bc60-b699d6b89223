# Generated by Django 5.0.6 on 2024-06-29 06:23

import core.dbfields
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('common', '0001_initial'),
        ('empresa', '0003_bancomovimento'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Terc<PERSON>',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo', models.CharField(max_length=1)),
                ('nome', core.dbfields.NameField(max_length=100)),
                ('foto', core.dbfields.ThumbnailImageField(blank=True, null=True, size=(200, 240), upload_to='terceiro_%Y')),
                ('morada', core.dbfields.AddressField(blank=True, max_length=150)),
                ('cpostal_alt', models.CharField(blank=True, max_length=50)),
                ('nif', core.dbfields.VATNOField(max_length=15, null=True, unique=True)),
                ('iban', core.dbfields.IBANField(blank=True, max_length=34)),
                ('bic', core.dbfields.BICField(blank=True, max_length=11)),
                ('niss', core.dbfields.NISSField(blank=True, max_length=11)),
                ('ncga', models.CharField(blank=True, max_length=10)),
                ('telefone', core.dbfields.PhoneField(blank=True, max_length=20)),
                ('fax', core.dbfields.PhoneField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('www', models.URLField(blank=True)),
                ('obs', models.TextField(blank=True)),
                ('di_tipo', models.IntegerField(blank=True, choices=[(1, 'Bilhete de Identidade'), (2, 'Cédula Pessoal'), (3, 'Passaporte'), (4, 'Autorização de Residência'), (5, 'Cartão do Cidadão'), (9, 'Outro')], null=True)),
                ('di_numero', models.CharField(blank=True, max_length=30)),
                ('di_data_emissao', models.DateField(blank=True, null=True)),
                ('di_data_validade', models.DateField(blank=True, null=True)),
                ('di_arquivo', models.CharField(blank=True, max_length=30)),
                ('data_nascimento', models.DateField(blank=True, null=True)),
                ('sexo', models.CharField(blank=True, choices=[('M', 'Masculino'), ('F', 'Feminino')], max_length=1)),
                ('formacao_academica', models.IntegerField(blank=True, choices=[(1, 'Licenciatura'), (2, 'Bacharelato'), (3, 'Pós-graduação'), (4, 'Mestrado'), (5, 'Doutoramento'), (6, 'Diploma de Estudos Superiores Especializados'), (7, 'Magistério Primário/Educadores de Infância'), (8, 'Secundário'), (9, 'Básico (3.º ciclo)'), (10, 'Básico (2.º ciclo)'), (11, 'Básico (1.º ciclo)'), (12, 'Sem Habilitação'), (99, 'Outra')], null=True)),
                ('situacao_emprego', models.IntegerField(blank=True, choices=[(1, 'Trabalhador por conta de outrem'), (2, 'Trabalhador por conta própria como isolado'), (3, 'Trabalhador por conta própria como empregador'), (4, 'Desempregado'), (5, 'Estudante'), (6, 'Doméstico'), (7, 'Reformado'), (8, 'Situação Desconhecida'), (9, 'Outra')], null=True)),
                ('empresa_trabalha', models.CharField(blank=True, max_length=100)),
                ('ws_escolaid', models.IntegerField(blank=True, null=True)),
                ('ws_terceiroid', models.IntegerField(blank=True, null=True)),
                ('ws_turma', models.CharField(blank=True, null=True, max_length=30)),
                ('cpostal', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='common.cpostal')),
                ('empresa', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='empresa.empresa')),
                ('morada_dcf', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='common.dcf')),
                ('nacionalidade', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='common.pais')),
                ('naturalidade', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='common.pais')),
                ('naturalidade_dcf', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='common.dcf')),
                ('profissao', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='common.profissao')),
                ('user', models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ('nome',),
            },
        ),
    ]
