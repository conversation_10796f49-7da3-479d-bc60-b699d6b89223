from django.db.models import F
from django.db.models.functions import Substr

from reports.reports import Report, Col, Group

from .models import Terceiro


class TerceiroReport(Report):
    qs = Terceiro.objects.only('nome','nif','telefone','email').annotate(first_letter=Substr(F('nome'), 1, 1))
    

    groups = [
        Group(exp='{row.first_letter}', caption='{row.first_letter}', print_on_new_page=True),
    ]

    cols = [
        Col(w=70, caption='Nome',     exp='{row.nome}'),
        Col(w=20, caption='NIF',      exp='{row.nif}'),
        Col(w=30, caption='Telefone', exp='{row.telefone}'),
        Col(w=40, caption='email',    exp='{row.email}'),
        Col(w=0),
    ]