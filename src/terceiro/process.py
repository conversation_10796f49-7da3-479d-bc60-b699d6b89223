from time import sleep
from core.decorators import task, cronjob



@cronjob("reembolsos workflow", "*:00/5")
def process_workflow():
    print("processing workflow")


@cronjob("pagamentos", "Sun 17:00")
def pagamentos_semanais():
    print("processing pagamentos")


@task
def send_email(fr:str, to:str, subject:str):
    print(f"sending email from:{fr} to:{to} subject:{subject}")
    sleep(2)


@task
def send_info(subject:str):
    print(f"another time consuming task subject:{subject}")
    sleep(1)    