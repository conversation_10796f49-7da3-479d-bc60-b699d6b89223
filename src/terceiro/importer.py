from core.importer import SQLImporter


class Terceiro(SQLImporter):
    filename = 'terceiros.csv'
    fields = 'nome,nif,di_tipo,di_numero,di_data_emissao,di_data_validade,di_arquivo,sexo,data_nascimento,nacionalidade,naturalidade,naturalidade_dcf,morada,cpostal_ws,MDCF,telefone,fax,email,www,iban,bic,niss,obs,formacao_academica,profissao_codigo,situacao_emprego,empresa_trabalha'
    skip_first_line = True
    encoding = 'latin-1'

    def after_import(self):
        self.runsql('''
            alter table tmp_terceiros add column id serial primary key;
            with dif_terceiros as (
                select nif _, max(id) mid 
                from tmp_terceiros 
                group by nif
            )
            insert into terceiro_terceiro(tipo, 
                nome, 
                nif, 
                iban, 
                bic, 
                niss, 
                ncga,
                telefone,
                fax,
                email,
                www,               
                morada, 
                cpostal_alt,
                di_tipo, 
                di_numero, 
                di_data_emissao,
                di_data_validade, 
                di_arquivo,
                sexo,
                data_nascimento,
                empresa_trabalha,
                obs
                )
            select '',
                nome, 
                nif, 
                coalesce(iban,''),
                coalesce(bic,''),
                coalesce(niss,''),
                '',
                coalesce(left(telefone,20),''),
                coalesce(left(fax,20),''),
                coalesce(email,''),
                coalesce(www,''),              
                coalesce(morada, ''), 
                coalesce(cpostal_ws,'') ,
                di_tipo::int, 
                coalesce(di_numero,''), 
                di_data_emissao::date,
                di_data_validade::date,
                coalesce(di_arquivo,''),
                coalesce(sexo,''),
                data_nascimento::date,
                '',
                ''
            from tmp_terceiros
            where id in (select mid from dif_terceiros);
        ''')