from fpdf import FPDF



class Group():
    def __init__(self, exp, caption='', force_new_page=False, print_on_new_page=False, data_header=False, footer=False):
        self.exp = exp
        self.caption = caption
        self.force_new_page = force_new_page
        self.print_on_new_page = print_on_new_page
        self.data_header = data_header
        self.footer = footer


class Col():
    def __init__(self, w=0, h=4, caption='', exp='', border=None, align=None, sum=False, hide_zero=False, hide_repeated=False):
        self.w = w
        self.h = h
        self.caption = caption
        self.exp = exp
        self.border = 'B' if border is None else border     
        self.align = 'L' if align is None else align
        self.sum = sum
        self.hide_zero = hide_zero
        self.hide_repeated = hide_repeated
        # internal
        self.sums = [0, 0, 0, 0, 0, 0]
        self.last_value = None
    
    def draw_data(self, rpt:FPDF, row):
        text = ''
        if self.exp:
            text = self.exp.format(row=row) if row else self.caption
        if self.hide_repeated and self.last_value == text:
            text = ''
        else:
            self.last_value = text
        if self.hide_zero and (text == '0' or text == '0.00'):
            text = '-'
        rpt.cell(self.w, self.h, text, self.border, align=self.align)

    def draw_caption(self, rpt:FPDF):
        rpt.cell(self.w, self.h, self.caption, border=1, align=self.align, fill=True)


    def sumup(self, row):
        if self.sum:
            try:
                val = float(self.exp.format(row=row))
            except Exception:
                val = 0
            for i in range(6):
                self.sums[i] += val

    def reset_sums(self, depth):
        if self.sum:
           for i in range(depth, 6):
                self.sums[i] = 0

    def reset_last_value(self):
        self.last_value = None




class Report(FPDF):
    format = "A4"
    orientation = "Portrait"
    default_page_header = True
    default_page_footer = True
    groups = []
    cols = []

    def __init__(self) -> None:
        super().__init__(self.orientation, "mm", self.format)

    def header(self):
        starting_report = (self.page == 1 and self.y == self.t_margin)
        if self.default_page_header:
            self.image("laranjeiras.png", 10, 10, h=15, w=40, keep_aspect_ratio=True)
            #self.set_font("helvetica", "B", 15)
            self.cell(40)
            self.cell(0, 10, "Title", align="C")
            self.ln(10)
            self.cell(40)
            self.cell(0, 5,  "Subtitle", align="C")
            # Performing a line break:
            self.ln(7)
        if not any(g.data_header for g in self.groups):
            self.data_header()
        if starting_report:
            ix = 0
        else :
            ix = -1 
            for i, g in enumerate(self.groups):
                if g.force_new_page or g.print_on_new_page:
                    ix = i
                    break
        if ix != -1:
            for i in range(ix, len(self.groups)):
                self.group_header(i)
        # changing page reset last_values
        for col in self.cols:
            col.reset_last_value()    

    def group_header(self, ix):
        if ix == 0:
            self.ln(2)
        self.cell(0,4,self.groups[ix].caption.format(row=self.row), fill=True, border='B')
        self.ln(4)
        if self.groups[ix].data_header:
            self.data_header()

    def group_footer(self, ix):
        if self.groups[ix].footer:  
            # self.cell(0,4,self.groups[ix].caption.format(row=self.row))
            # self.ln(4)
            has_sums = False
            for col in self.cols:
                text = ''
                if col.sum:
                    text = f'{col.sums[ix]:10.2f}'
                    has_sums = True
                self.cell(col.w, col.h, text, align=col.align, border=0)
            if has_sums:
                self.ln(4)

    def data_header(self):
        for col in self.cols:
            col.draw_caption(self)
        self.ln(4)

    def data(self, row):
        for col in self.cols:
            col.draw_data(self, row)
        self.ln(4)

    def footer(self):
        if self.default_page_footer:
            self.set_y(-15)
            self.set_font("helvetica", "I", 8)
            self.cell(0, 8, f"Page {self.page_no()}/{{nb}}", align="R", border=0)

    def render(self, filename:str) -> None:
        self.set_font("helvetica", "", 8)
        self.set_fill_color(200)

        # reset sumups & last value
        for col in self.cols:
            col.reset_sums(0)
            col.reset_last_value()
        old_group_vals = [None for g in self.groups]        
        self.row = None
        in_report = False
        for row in self.qs:
            self.previous_row = self.row
            self.row = row
            if not in_report:
                self.add_page()
            group_vals = [g.exp.format(row=row) for g in self.groups]
            if in_report:
                ix = -1
                for i in range(len(self.groups)):
                    if old_group_vals[i] != group_vals[i]:
                        ix = i
                        break
                if ix != -1: # some groups breaked
                    # print group footers
                    # context should be last row in group 
                    # a page break coulf occur printing footers
                    tmp_page = self.page
                    tmp_row = self.row
                    self.row = self.previous_row
                    for i in reversed(range(ix, len(self.groups))):
                        self.group_footer(i)
                    self.row = tmp_row

                    # reset sumups
                    for col in self.cols:
                        col.reset_sums(ix)
                        col.reset_last_value()

                    if tmp_page == self.page: 
                        # if on same page we need to print groups headers
                        h = (len(self.groups) - ix) * 4 + 4 # height of needed groups header plus one datarow
                        #h = sum(gh.h for gh in self.group_headers[ix:])
                        force_new_page = any(gh.force_new_page for gh in self.groups[ix:])
                        if force_new_page or self.will_page_break(h):
                            self.add_page()
                        else:
                            for i in range(ix, len(self.groups)):
                                self.group_header(i)
            
            if self.will_page_break(4):
                self.add_page()            
            self.data(row)
            in_report = True
            # update sumups
            for col in self.cols:
                col.sumup(row)
            old_group_vals = group_vals 

        # print final group footers
        for i in reversed(range(len(self.groups))):
            self.group_footer(i)    
            
        return self.output(filename)
