from django import forms
from django.core.exceptions import ValidationError
from .models import Doc, DocumentUploadBatch


class MultipleFileInput(forms.ClearableFileInput):
    """Custom widget for multiple file uploads"""
    allow_multiple_selected = True


class MultipleFileField(forms.FileField):
    """Custom field for handling multiple file uploads"""
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("widget", MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data, initial=None):
        single_file_clean = super().clean
        if isinstance(data, (list, tuple)):
            result = [single_file_clean(d, initial) for d in data]
        else:
            result = single_file_clean(data, initial)
        return result


class DocumentUploadForm(forms.Form):
    """Form for uploading multiple documents"""
    batch_name = forms.CharField(
        max_length=255,
        help_text="Descriptive name for this upload batch (e.g., 'June 2024 Invoices')",
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter a name for this upload batch'
        })
    )
    
    files = MultipleFileField(
        help_text="Select one or more files to upload (PDF, PNG, JPG, JPEG supported)",
        widget=MultipleFileInput(attrs={
            'class': 'form-control',
            'accept': '.pdf,.png,.jpg,.jpeg',
            'multiple': True
        })
    )
    
    auto_suggest_associations = forms.BooleanField(
        required=False,
        initial=True,
        help_text="Automatically suggest associations with existing records",
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )

    def clean_files(self):
        """Validate uploaded files"""
        files = self.cleaned_data.get('files', [])
        
        if not files:
            raise ValidationError("Please select at least one file to upload.")
        
        # Ensure files is a list
        if not isinstance(files, list):
            files = [files]
        
        # Validate file types and sizes
        allowed_extensions = ['.pdf', '.png', '.jpg', '.jpeg']
        max_file_size = 10 * 1024 * 1024  # 10MB
        
        for file in files:
            # Check file extension
            file_extension = file.name.lower().split('.')[-1]
            if f'.{file_extension}' not in allowed_extensions:
                raise ValidationError(
                    f"File '{file.name}' has an unsupported format. "
                    f"Allowed formats: {', '.join(allowed_extensions)}"
                )
            
            # Check file size
            if file.size > max_file_size:
                raise ValidationError(
                    f"File '{file.name}' is too large. Maximum size is 10MB."
                )
        
        return files


class DocumentAssociationReviewForm(forms.Form):
    """Form for reviewing and validating document associations"""
    
    def __init__(self, associations, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Create fields for each association
        for association in associations:
            field_name = f'association_{association.id}'
            choices = [
                ('approve', 'Approve'),
                ('reject', 'Reject'),
                ('pending', 'Keep Pending')
            ]
            
            self.fields[field_name] = forms.ChoiceField(
                choices=choices,
                initial='pending',
                widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
                label=f'{association.doc.name} → {association.associated_object}'
            )
            
            # Add a notes field for each association
            notes_field_name = f'notes_{association.id}'
            self.fields[notes_field_name] = forms.CharField(
                required=False,
                widget=forms.Textarea(attrs={
                    'class': 'form-control',
                    'rows': 2,
                    'placeholder': 'Optional notes about this association...'
                }),
                label='Notes'
            )

    def get_association_decisions(self):
        """Extract association decisions from cleaned data"""
        decisions = {}
        notes = {}
        
        for field_name, value in self.cleaned_data.items():
            if field_name.startswith('association_'):
                association_id = int(field_name.split('_')[1])
                decisions[association_id] = value
            elif field_name.startswith('notes_'):
                association_id = int(field_name.split('_')[1])
                notes[association_id] = value
        
        return decisions, notes


class DocumentSearchForm(forms.Form):
    """Form for searching and filtering documents"""
    search_query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search documents by name or content...'
        })
    )
    
    upload_batch = forms.ModelChoiceField(
        queryset=DocumentUploadBatch.objects.all(),
        required=False,
        empty_label="All batches",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    has_associations = forms.ChoiceField(
        choices=[
            ('', 'All documents'),
            ('yes', 'With associations'),
            ('no', 'Without associations'),
            ('pending', 'Pending associations'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
