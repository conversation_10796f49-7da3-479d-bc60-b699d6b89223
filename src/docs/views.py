from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.views.decorators.http import require_http_methods

from .models import Doc, DocumentUploadBatch, DocumentAssociation
from .forms import DocumentUploadForm, DocumentAssociationReviewForm, DocumentSearchForm


def doc_view(request, doc_id):
    context = {
        "doc": Doc.objects.get(pk=doc_id)
    }
    return render(request, "docs/doc_modal.html", context)


@login_required
def upload_documents(request):
    """View for uploading multiple documents"""
    if request.method == 'POST':
        form = DocumentUploadForm(request.POST, request.FILES)
        if form.is_valid():
            # Create upload batch
            batch = DocumentUploadBatch.objects.create(
                name=form.cleaned_data['batch_name'],
                uploaded_by=request.user,
                status='processing'
            )

            # Process uploaded files
            files = form.cleaned_data['files']
            if not isinstance(files, list):
                files = [files]

            created_docs = []
            for file in files:
                doc = Doc.objects.create(
                    name=file.name,
                    file=file,
                    upload_batch=batch
                )
                created_docs.append(doc)

            # Update batch statistics
            batch.total_docs = len(created_docs)
            batch.save()

            # Generate associations if requested
            if form.cleaned_data['auto_suggest_associations']:
                from .utils import generate_document_associations
                generate_document_associations(batch)

            batch.status = 'pending_review' if batch.total_associations > 0 else 'completed'
            batch.save()

            messages.success(
                request,
                f'Successfully uploaded {len(created_docs)} documents. '
                f'Batch: {batch.name}'
            )

            if batch.total_associations > 0:
                return redirect('docs:review_associations', batch_id=batch.id)
            else:
                return redirect('docs:document_list')
    else:
        form = DocumentUploadForm()

    return render(request, 'docs/upload.html', {'form': form})


@login_required
def document_list(request):
    """View for listing and searching documents"""
    form = DocumentSearchForm(request.GET)
    docs = Doc.objects.select_related('upload_batch').prefetch_related('associations')

    if form.is_valid():
        # Apply search filters
        search_query = form.cleaned_data.get('search_query')
        if search_query:
            docs = docs.filter(
                Q(name__icontains=search_query) |
                Q(qrcode_data__icontains=search_query)
            )

        upload_batch = form.cleaned_data.get('upload_batch')
        if upload_batch:
            docs = docs.filter(upload_batch=upload_batch)

        date_from = form.cleaned_data.get('date_from')
        if date_from:
            docs = docs.filter(upload_batch__uploaded_at__date__gte=date_from)

        date_to = form.cleaned_data.get('date_to')
        if date_to:
            docs = docs.filter(upload_batch__uploaded_at__date__lte=date_to)

        has_associations = form.cleaned_data.get('has_associations')
        if has_associations == 'yes':
            docs = docs.filter(associations__isnull=False).distinct()
        elif has_associations == 'no':
            docs = docs.filter(associations__isnull=True)
        elif has_associations == 'pending':
            docs = docs.filter(associations__status='pending').distinct()

    # Add association counts
    docs = docs.annotate(
        total_associations=Count('associations'),
        pending_associations=Count('associations', filter=Q(associations__status='pending')),
        approved_associations=Count('associations', filter=Q(associations__status__in=['approved', 'auto_approved']))
    )

    # Pagination
    paginator = Paginator(docs.order_by('-id'), 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'page_obj': page_obj,
        'docs': page_obj.object_list
    }

    return render(request, 'docs/document_list.html', context)


@login_required
def review_associations(request, batch_id):
    """View for reviewing document associations in a batch"""
    batch = get_object_or_404(DocumentUploadBatch, id=batch_id)
    associations = DocumentAssociation.objects.filter(
        doc__upload_batch=batch,
        status='pending'
    ).select_related('doc', 'content_type').order_by('-confidence_score')

    if request.method == 'POST':
        form = DocumentAssociationReviewForm(associations, request.POST)
        if form.is_valid():
            decisions, notes = form.get_association_decisions()

            # Process each decision
            for association_id, decision in decisions.items():
                association = associations.get(id=association_id)
                note = notes.get(association_id, '')

                if decision == 'approve':
                    association.approve(user=request.user)
                elif decision == 'reject':
                    association.reject(user=request.user)

                # Add notes to suggestion_reason if provided
                if note:
                    association.suggestion_reason += f"\n\nUser notes: {note}"
                    association.save()

            # Update batch statistics
            batch.update_statistics()

            messages.success(request, f'Association review completed for batch: {batch.name}')
            return redirect('docs:document_list')
    else:
        form = DocumentAssociationReviewForm(associations)

    context = {
        'batch': batch,
        'associations': associations,
        'form': form
    }

    return render(request, 'docs/review_associations.html', context)


@login_required
def batch_list(request):
    """View for listing upload batches"""
    batches = DocumentUploadBatch.objects.select_related('uploaded_by').order_by('-uploaded_at')

    # Pagination
    paginator = Paginator(batches, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'batches': page_obj.object_list
    }

    return render(request, 'docs/batch_list.html', context)


@login_required
def batch_detail(request, batch_id):
    """View for detailed batch information"""
    batch = get_object_or_404(DocumentUploadBatch, id=batch_id)
    docs = batch.documents.all().prefetch_related('associations')
    associations = DocumentAssociation.objects.filter(doc__upload_batch=batch)

    context = {
        'batch': batch,
        'docs': docs,
        'associations': associations
    }

    return render(request, 'docs/batch_detail.html', context)


@login_required
@require_http_methods(["POST"])
def quick_association_action(request):
    """AJAX view for quick association approval/rejection"""
    association_id = request.POST.get('association_id')
    action = request.POST.get('action')

    try:
        association = DocumentAssociation.objects.get(id=association_id)

        if action == 'approve':
            association.approve(user=request.user)
            message = 'Association approved'
        elif action == 'reject':
            association.reject(user=request.user)
            message = 'Association rejected'
        else:
            return JsonResponse({'success': False, 'error': 'Invalid action'})

        # Update batch statistics
        if association.doc.upload_batch:
            association.doc.upload_batch.update_statistics()

        return JsonResponse({'success': True, 'message': message})

    except DocumentAssociation.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Association not found'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
