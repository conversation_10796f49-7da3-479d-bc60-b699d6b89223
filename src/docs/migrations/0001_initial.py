# Generated by Django 5.0.6 on 2024-06-28 08:56

import docs.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Doc',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('file', models.FileField(upload_to=docs.models.doc_file_name)),
                ('thumbnail', models.ImageField(blank=True, null=True, upload_to=docs.models.thumb_file_name)),
                ('qrcode_data', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='EmailFetchState',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_uid', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='Email',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_id', models.CharField(max_length=255, unique=True)),
                ('subject', models.CharField(max_length=255)),
                ('sender', models.EmailField(max_length=254)),
                ('received_at', models.DateTimeField()),
                ('body', models.TextField()),
                ('attachments', models.ManyToManyField(related_name='emails', to='docs.doc')),
            ],
        ),
    ]
