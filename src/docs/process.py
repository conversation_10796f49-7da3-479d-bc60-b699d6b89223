import imaplib
import email
from email.policy import default

from django.core.files.base import ContentFile
from django.conf import settings

from .models import Doc, EmailFetchState, Email 



def fetch_and_save_emails():
    # Get the last fetched UID
    fetch_state, created = EmailFetchState.objects.get_or_create(id=1)
    last_uid = fetch_state.last_uid
    done_uid = 0

    mail = imaplib.IMAP4_SSL(settings.EMAIL_HOST_IMAP)
    mail.login(settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD)
    mail.select('inbox')
    
    # Search for new emails
    result, data = mail.uid('search', None, f'UID {last_uid+1}:*')
    uids = data[0].split()

    for uid in uids:
        result, data = mail.uid('fetch', uid, '(RFC822)')
        raw_email = data[0][1]
        msg = email.message_from_bytes(raw_email, policy=default)

        # Check if the email already exists
        if Email.objects.filter(message_id=msg['Message-ID']).exists():
            continue

        print(uid, msg['subject'], msg['from'])
        body = msg.get_body()
        if body:
            body = body.get_payload(decode=True)
        # Save email
        email_obj = Email(
            message_id=msg['Message-ID'],
            subject=msg['subject'],
            sender=msg['from'],
            received_at=email.utils.parsedate_to_datetime(msg['date']),
            body=body.decode('utf-8', errors='ignore') if body else ''
        )
        email_obj.save()

        for part in msg.walk():
            if not part.is_attachment():
                continue
            try:
                attachment_data = part.get_payload(decode=True)
                attachment_name = part.get_filename()
                if attachment_name.endswith('.p7s'):
                    continue
                attachment = Doc(
                    name=attachment_name,
                    file=ContentFile(attachment_data, name=attachment_name)
                )
                attachment.save()
                email_obj.attachments.add(attachment)
            except Exception as e:
                print('\t===> ',e)

        # Save the email again to update the many-to-many relationship
        email_obj.save()
        done_uid = uid

    mail.logout()

    # Update the last fetched UID
    if done_uid:
        fetch_state.last_uid = int(done_uid)
        fetch_state.save()
