from PIL import Image
import io
from pathlib import Path
from datetime import date

from django.db import models
from django.core.files.base import ContentFile

# from pyzbar.pyzbar import decode, ZBarSymbol
import cv2
import numpy as np
from unidecode import unidecode

from pdf2image import convert_from_path

THUMBNAIL_SIZE = (200,200)

def proper_filename(name):
    pfn = Path(name)
    stem, suffix = pfn.stem, pfn.suffix
    return unidecode(stem).replace('-',' ').replace('.','').replace('  ',' ')+suffix


def doc_file_name(instance, filename):
    year = date.today().year
    return f'docs_{year}/{proper_filename(instance.name)}'

def thumb_file_name(instance, filename):
    year = date.today().year
    return f'docs_{year}/{Path(proper_filename(instance.name)).with_suffix('.thumb.png')}'


class Doc(models.Model):
    name = models.CharField(max_length=255)
    file = models.FileField(upload_to=doc_file_name)
    thumbnail = models.ImageField(upload_to=thumb_file_name, null=True, blank=True)
    qrcode_data = models.TextField(null=True, blank=True)

    def __str__(self):
        return f'{self.pk} - {self.name} {self.file} qrcode:{self.qrcode_data}'

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.process_qrcode()
        self.generate_thumbnail()
        super().save(*args, **kwargs)

    def process_qrcode(self):
        file_ext = Path(self.file.name).suffix.lower()
        if file_ext in ['.png', '.jpg', '.jpeg']:
            self._process_image_qrcode()
        elif file_ext == '.pdf':
            self._process_pdf_qrcode()

    def _process_image_qrcode(self):
        detector = cv2.QRCodeDetector()
        open_cv_image = cv2.imread(self.file.path)
        _, qrcodes, _, _= detector.detectAndDecodeMulti(open_cv_image)
        for qrcode in qrcodes:
            self.qrcode_data = qrcode
            if self.qrcode_data.startswith('A:'):
                return
    
    def _process_pdf_qrcode(self):
        detector = cv2.QRCodeDetector()
        pages = convert_from_path(self.file.path)
        for page in pages:
            open_cv_image = cv2.cvtColor(np.array(page), cv2.COLOR_RGB2BGR)
            _, qrcodes, _, _= detector.detectAndDecodeMulti(open_cv_image)
            for qrcode in qrcodes:
                self.qrcode_data = qrcode
                if self.qrcode_data.startswith('A:'):
                    return

    def generate_thumbnail(self):
        file_ext = Path(self.file.name).suffix.lower()
        if file_ext in ['.png', '.jpg', '.jpeg']:
            self._generate_image_thumbnail()
        elif file_ext == '.pdf':
            self._generate_pdf_thumbnail()

    def _generate_image_thumbnail(self):
        with self.file.open('rb') as f:
            img = Image.open(f)
            img.thumbnail(THUMBNAIL_SIZE)
            thumb_io = io.BytesIO()
            img.save(thumb_io, format='PNG')
            thumbnail = ContentFile(thumb_io.getvalue(), name='x.png')
            self.thumbnail.save(thumbnail.name, thumbnail, save=False)

    def _generate_pdf_thumbnail(self):
        pages = convert_from_path(self.file.path, last_page=1)
        img = pages[0]
        img.thumbnail(THUMBNAIL_SIZE)
        thumb_io = io.BytesIO()
        img.save(thumb_io, format='PNG')
        thumbnail = ContentFile(thumb_io.getvalue(), name='x.png')
        self.thumbnail.save(thumbnail.name, thumbnail, save=False)


class EmailFetchState(models.Model):
    last_uid = models.IntegerField(default=0)
    
    def __str__(self):
        return f"Last UID: {self.last_uid}"
    

class Email(models.Model):
    message_id = models.CharField(max_length=255, unique=True)
    subject = models.CharField(max_length=255)
    sender = models.EmailField()
    received_at = models.DateTimeField()
    body = models.TextField()
    attachments = models.ManyToManyField('Doc', related_name='emails')

    def __str__(self):
        return f'{self.received_at} {self.subject}'