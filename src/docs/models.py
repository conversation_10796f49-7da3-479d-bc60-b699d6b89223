from PIL import Image
import io
from pathlib import Path
from datetime import date

from django.db import models
from django.core.files.base import ContentFile
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.utils import timezone

# from pyzbar.pyzbar import decode, ZBarSymbol
import cv2
import numpy as np
from unidecode import unidecode

from pdf2image import convert_from_path

THUMBNAIL_SIZE = (200,200)

def proper_filename(name):
    pfn = Path(name)
    stem, suffix = pfn.stem, pfn.suffix
    return unidecode(stem).replace('-',' ').replace('.','').replace('  ',' ')+suffix


def doc_file_name(instance, filename):
    year = date.today().year
    return f'docs_{year}/{proper_filename(instance.name)}'

def thumb_file_name(instance, filename):
    year = date.today().year
    return f'docs_{year}/{Path(proper_filename(instance.name)).with_suffix('.thumb.png')}'


class Doc(models.Model):
    name = models.CharField(max_length=255)
    file = models.FileField(upload_to=doc_file_name)
    thumbnail = models.ImageField(upload_to=thumb_file_name, null=True, blank=True)
    qrcode_data = models.TextField(null=True, blank=True)
    upload_batch = models.ForeignKey(
        'DocumentUploadBatch',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='documents'
    )

    def __str__(self):
        return f'{self.pk} - {self.name} {self.file} qrcode:{self.qrcode_data}'

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.process_qrcode()
        self.generate_thumbnail()
        super().save(*args, **kwargs)

    def process_qrcode(self):
        file_ext = Path(self.file.name).suffix.lower()
        if file_ext in ['.png', '.jpg', '.jpeg']:
            self._process_image_qrcode()
        elif file_ext == '.pdf':
            self._process_pdf_qrcode()

    def _process_image_qrcode(self):
        detector = cv2.QRCodeDetector()
        open_cv_image = cv2.imread(self.file.path)
        _, qrcodes, _, _= detector.detectAndDecodeMulti(open_cv_image)
        for qrcode in qrcodes:
            self.qrcode_data = qrcode
            if self.qrcode_data.startswith('A:'):
                return
    
    def _process_pdf_qrcode(self):
        detector = cv2.QRCodeDetector()
        pages = convert_from_path(self.file.path)
        for page in pages:
            open_cv_image = cv2.cvtColor(np.array(page), cv2.COLOR_RGB2BGR)
            _, qrcodes, _, _= detector.detectAndDecodeMulti(open_cv_image)
            for qrcode in qrcodes:
                self.qrcode_data = qrcode
                if self.qrcode_data.startswith('A:'):
                    return

    def generate_thumbnail(self):
        file_ext = Path(self.file.name).suffix.lower()
        if file_ext in ['.png', '.jpg', '.jpeg']:
            self._generate_image_thumbnail()
        elif file_ext == '.pdf':
            self._generate_pdf_thumbnail()

    def _generate_image_thumbnail(self):
        with self.file.open('rb') as f:
            img = Image.open(f)
            img.thumbnail(THUMBNAIL_SIZE)
            thumb_io = io.BytesIO()
            img.save(thumb_io, format='PNG')
            thumbnail = ContentFile(thumb_io.getvalue(), name='x.png')
            self.thumbnail.save(thumbnail.name, thumbnail, save=False)

    def _generate_pdf_thumbnail(self):
        pages = convert_from_path(self.file.path, last_page=1)
        img = pages[0]
        img.thumbnail(THUMBNAIL_SIZE)
        thumb_io = io.BytesIO()
        img.save(thumb_io, format='PNG')
        thumbnail = ContentFile(thumb_io.getvalue(), name='x.png')
        self.thumbnail.save(thumbnail.name, thumbnail, save=False)


class EmailFetchState(models.Model):
    last_uid = models.IntegerField(default=0)
    
    def __str__(self):
        return f"Last UID: {self.last_uid}"
    

class Email(models.Model):
    message_id = models.CharField(max_length=255, unique=True)
    subject = models.CharField(max_length=255)
    sender = models.EmailField()
    received_at = models.DateTimeField()
    body = models.TextField()
    attachments = models.ManyToManyField('Doc', related_name='emails')

    def __str__(self):
        return f'{self.received_at} {self.subject}'


class DocumentAssociation(models.Model):
    """
    Model to handle suggested associations between documents and other model instances.
    Supports validation workflow where users can approve or reject suggestions.
    """
    ASSOCIATION_STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('auto_approved', 'Auto Approved'),
    ]

    doc = models.ForeignKey('Doc', on_delete=models.CASCADE, related_name='associations')

    # Generic foreign key to associate with any model
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    associated_object = GenericForeignKey('content_type', 'object_id')

    # Association metadata
    status = models.CharField(max_length=20, choices=ASSOCIATION_STATUS_CHOICES, default='pending')
    confidence_score = models.FloatField(
        help_text="Confidence score (0.0-1.0) for the suggested association"
    )
    suggestion_reason = models.TextField(
        help_text="Explanation of why this association was suggested"
    )

    # Timestamps and user tracking
    created_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    reviewed_by = models.ForeignKey(
        'auth.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="User who reviewed this association"
    )

    class Meta:
        unique_together = ('doc', 'content_type', 'object_id')
        ordering = ['-confidence_score', '-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['content_type', 'object_id']),
        ]

    def __str__(self):
        return f'{self.doc.name} -> {self.associated_object} ({self.status})'

    def approve(self, user=None):
        """Approve the association and update the target object if applicable"""
        self.status = 'approved'
        self.reviewed_at = timezone.now()
        self.reviewed_by = user
        self.save()

        # Update the target object's doc field if it has one
        target_obj = self.associated_object
        if hasattr(target_obj, 'doc') and target_obj.doc is None:
            target_obj.doc = self.doc
            target_obj.save()

    def reject(self, user=None):
        """Reject the association"""
        self.status = 'rejected'
        self.reviewed_at = timezone.now()
        self.reviewed_by = user
        self.save()


class DocumentUploadBatch(models.Model):
    """
    Model to group documents uploaded together and track the association process.
    """
    BATCH_STATUS_CHOICES = [
        ('processing', 'Processing'),
        ('pending_review', 'Pending Review'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    name = models.CharField(max_length=255, help_text="Descriptive name for this upload batch")
    status = models.CharField(max_length=20, choices=BATCH_STATUS_CHOICES, default='processing')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey('auth.User', on_delete=models.CASCADE)

    # Statistics
    total_docs = models.PositiveIntegerField(default=0)
    total_associations = models.PositiveIntegerField(default=0)
    approved_associations = models.PositiveIntegerField(default=0)
    rejected_associations = models.PositiveIntegerField(default=0)

    # Processing metadata
    processing_notes = models.TextField(blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-uploaded_at']

    def __str__(self):
        return f'{self.name} ({self.total_docs} docs, {self.status})'

    @property
    def pending_associations(self):
        """Count of associations still pending review"""
        return self.total_associations - self.approved_associations - self.rejected_associations

    def update_statistics(self):
        """Update association statistics"""
        docs = Doc.objects.filter(upload_batch=self)
        associations = DocumentAssociation.objects.filter(doc__in=docs)

        self.total_associations = associations.count()
        self.approved_associations = associations.filter(status__in=['approved', 'auto_approved']).count()
        self.rejected_associations = associations.filter(status='rejected').count()

        # Update status based on progress
        if self.pending_associations == 0 and self.total_associations > 0:
            self.status = 'completed'
            if not self.completed_at:
                self.completed_at = timezone.now()
        elif self.total_associations > 0:
            self.status = 'pending_review'

        self.save()