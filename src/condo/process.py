
from collections import defaultdict
from datetime import date, datetime, timedelta
from decimal import Decimal

from django.db.models import Sum, OuterRef, Value
from django.db.models.functions import Concat
from django.template.loader import render_to_string

from core.constants import MONTHS
from core.email import EmailHtml

from .models import Condo, Orcamento, OrcamentoLinha, Fracao, Receita, QUOTA_CHOICES, EVReading



def aplicar_orcamento(condo:Condo, year:int)-> None:
    qs = Receita.objects.filter(fracao__condo=condo, tipo__in=['Q','F'], data__year=year)
    if qs.exists():
        return
        # print("Já foi aplicado o Orçamento")
        # if input("reaplicar?") not in "sSyY":
        #     return
        # qs.delete()

    orcamento = Orcamento.objects.get(condo=condo, ano=year)
    valor_orcamento = orcamento.rubricas.aggregate(total=Sum("valor", default=0))['total']

    for fracao in condo.fracao_set.all():
        quota = round(valor_orcamento * fracao.permilagem/100/1000 / 12, 2)
        fcr = round(quota * orcamento.fcr / 100 ,2)
        for mes in [1,2,3,4,5,6,7,8,9,10,11,12]:
            Receita.objects.create(
                tipo='Q', 
                fracao=fracao, 
                data=date(year, mes, 1), 
                descricao=QUOTA_CHOICES['Q'] + ' ' + MONTHS[mes-1], 
                valor=quota)
            Receita.objects.create(
                tipo='F', 
                fracao=fracao, 
                data=date(year, mes, 1), 
                descricao=QUOTA_CHOICES['F'] + ' ' + MONTHS[mes-1],
                valor=fcr)


def aplicar_multas(condo):
    """
    Apply a 50% penalty (tipo='M') for overdue quotas (tipo='Q') that:
      - Have valor_por_pagar > 0
      - Are more than 30 days past the end of their reference month
      - Do not already have a corresponding penalty (matched by fracao and description)

    Penalty receipts use today's date and prepend 'Multa atraso - ' to the original description.
    """
    today = date.today()
    overdue_date = today - timedelta(days=61)
    prefix = "Multa atraso - "

    overdue_quotas = (Receita.objects
        .filter(
            fracao__condo=condo, 
            tipo='Q', 
            valor_por_pagar__gt=0, 
            data__lt=overdue_date)
        .exclude( 
            fracao__receita__descricao=Concat(Value(prefix),OuterRef('descricao'))
        )
    )

    # Prepare penalty entries
    for quota in overdue_quotas:
        print(quota)
        Receita.objects.create(
            tipo='M',
            fracao=quota.fracao,
            data=date(today.year, today.month, 1),
            descricao=f"{prefix}{quota.descricao}",
            valor=quota.valor * Decimal('0.5')
        )


# Define the electricity rate per kWh.
ELECTRICITY_RATE_PER_KWH = Decimal('0.2') # Example rate: 0.20 EUR/kWh

def process_ev_readings(condo: Condo) -> None:
    """
    Processes EVReadings that haven't been billed yet and creates Receita entries.
    """
    unbilled_readings = EVReading.objects.filter(
        fracao__condo=condo,
        receita__isnull=True,
        consumo_calculado__isnull=False
    ).select_related('fracao')

    if not unbilled_readings.exists():
        print(f"No new EV readings to process for {condo}.")
        return

    print(f"Processing {unbilled_readings.count()} EV readings for {condo}...")
    for reading in unbilled_readings:
        if reading.consumo_calculado <= 0:
            print(f"Skipping EV reading for {reading.fracao} on {reading.data} due to non-positive consumption: {reading.consumo_calculado} kWh.")
            continue

        cost = reading.consumo_calculado * ELECTRICITY_RATE_PER_KWH
        cost = cost.quantize(Decimal('0.01')) # Round to 2 decimal places

        new_receita = Receita.objects.create(
            tipo='E', # 'E' for Eletricidade EV
            fracao=reading.fracao,
            data=reading.data,
            descricao=f"{QUOTA_CHOICES['E']} - {reading.consumo_calculado} kWh * {ELECTRICITY_RATE_PER_KWH} €",
            valor=cost
        )
        reading.receita = new_receita
        reading.save()
        # print(f"Created Receita for {reading.fracao}: {cost}€ (Consumption: {reading.consumo_calculado} kWh on {reading.data})")


def enviar_avisos(condo):
    print(f"\nProcessing EV readings before sending notices for {condo}...")
    process_ev_readings(condo)

    print(f"\nApply delay fines before sending notices for {condo}...")
    aplicar_multas(condo)

    print(f"\nPreparing to send debit notices for {condo}...")
    today = datetime.today()
    qs = (Receita.objects
        .filter(
            fracao__condo=condo, 
            valor_por_pagar__gt=0,
            data__lte=today
            # fracao__proprietario__nif__in=['216527350']
        )
        .select_related('fracao__proprietario')
        .order_by('fracao__nome_abrev', 'data')
    )
    
    # agrupar por terceiro
    grouped_data = defaultdict(list)
    for receita in qs:
        grouped_data[receita.fracao.proprietario].append(receita)
    
    for terceiro, lista in grouped_data.items():
        # para cada proprietário compor html enviar email
        total = sum(r.valor_por_pagar for r in lista)
        context = {
            'condo': condo,
            'terceiro': terceiro,
            'lista': lista,
            'total': total
        }
        print(f'Aviso {str(terceiro):60} {total:10.2f} ({len(lista)})')
        html_msg = render_to_string('condo/email_aviso_debitos.html', context)
        msg = EmailHtml(f'{condo.nome} - Aviso de Débito {MONTHS[today.month-1]}', to=[terceiro.email])
        msg.attach_html(html_msg)
        msg.send()


def enviar_resumo_irs(condo, ano, terceiro=None):
    """
    Envia um resumo anual de IRS para o proprietário.
    """
    qs = Receita.objects.filter(fracao__condo=condo, data__year=ano)
    if terceiro is not None:
        qs = qs.filter(fracao__proprietario=terceiro)
    qs = (qs
        .select_related('fracao', 'fracao__proprietario')
        .order_by('fracao__nome_abrev', 'data')
    )

    # agrupar por terceiro
    grouped_data = defaultdict(list)
    for receita in qs:
        grouped_data[receita.fracao.proprietario].append(receita)
    
    for terceiro, lista in grouped_data.items():
        # para cada proprietário compor html enviar email
        total_pago = sum(r.valor_pago for r in lista)
        context = {
            'condo': condo,
            'ano': ano,
            'terceiro': terceiro,
            'lista': lista,
            'total_pago': total_pago
        }
        print(f'Aviso {str(terceiro):60} {total_pago:10.2f} ({len(lista)})')
        html_msg = render_to_string('condo/email_resumo_irs.html', context)
        msg = EmailHtml(f'{condo.nome} - Declaração anual de pagamentos {ano}', to=[terceiro.email])
        msg.attach_html(html_msg)
        msg.send()
