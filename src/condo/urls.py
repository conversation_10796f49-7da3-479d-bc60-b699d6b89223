from django.urls import path

from .views import (
    dashboard_view,
    dashboard_totals_view,
    despesas_ct_view,
    receitas_ct_view,
    bancomovimentos_view,
    bancomovimentos_por_justificar_view,
    despesas_rubrica_view,
    receita_fracao_view,
    receita_terceiro_view,

    despesa_from_bancomovimento_view,

    send_overview_view,
    send_email_view,

    new_receitas_ct_view,
    new_receita_fracao_view,
    new_despesas_ct_view,
    new_despesas_rubrica,
    banco_movimentos_monthly_view,
    bm_justificacao_view,
)

app_name = "condo"
urlpatterns = [
    path('', dashboard_view, name='dashboard'),

    path('new_receitas_ct', new_receitas_ct_view, name='new_receitas_ct'),
    path('new_receitas_fracao/<int:fracao_id>', new_receita_fracao_view, name='new_receitas_fracao'),

    path('new_despesas_ct', new_despesas_ct_view, name='new_despesas_ct'),
    path('new_despesas_rubrica/<int:rubrica_id>', new_despesas_rubrica, name='new_despesas_rubrica'),

    path('banco_movimentos_monthly', banco_movimentos_monthly_view, name='banco_movimentos_monthly'),
    path('bm_justificacaoview/<int:id>', bm_justificacao_view, name='bm_justificacao_view'),



    path('totals', dashboard_totals_view, name='dashboard_totals'),
    path('despesas_ct', despesas_ct_view, name='despesas_ct'),
    path('receitas_ct', receitas_ct_view, name='receitas_ct'),
    path('bancomovimentos', bancomovimentos_view, name='bancomovimentos'),
    path('bancomovimentos_por_justificar', bancomovimentos_por_justificar_view, name='bancomovimentos_por_justificar'),

    path('despesas_rubrica/<int:rubrica_id>', despesas_rubrica_view, name='despesas_rubrica'),
    path('receitas_fracao/<int:fracao_id>', receita_fracao_view, name='receitas_fracao'),
    path('receitas_terceiro/<int:terceiro_id>', receita_terceiro_view, name='receitas_terceiro'),


    path('despesa_from_bancomovimento/<int:bm_id>', despesa_from_bancomovimento_view, name='despesa_from_bancomovimento'),

    path('send_overview', send_overview_view, name="send_overview"),
    path('send_email', send_email_view, name="send_email"),

]
