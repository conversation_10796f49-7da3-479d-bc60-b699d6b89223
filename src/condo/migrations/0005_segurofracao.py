# Generated by Django 5.2.1 on 2025-05-17 17:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('condo', '0004_alter_receita_tipo_evreading'),
    ]

    operations = [
        migrations.CreateModel(
            name='SeguroFracao',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ano_vigencia', models.IntegerField(help_text='Ano para o qual o seguro é válido', verbose_name='Ano de Vigência')),
                ('comprovativo', models.FileField(help_text='Documento comprovativo do seguro (e.g., PDF, imagem)', upload_to='seguros_fracao/%Y/', verbose_name='Comprovativo do Seguro')),
                ('data_envio_comprovativo', models.DateField(blank=True, help_text='Data em que o proprietário enviou o comprovativo', null=True, verbose_name='Data de Envio do Comprovativo')),
                ('criado_em', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('atualizado_em', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('fracao', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='seguros', to='condo.fracao', verbose_name='Fração')),
            ],
            options={
                'verbose_name': 'Seguro da Fração',
                'verbose_name_plural': 'Seguros das Frações',
                'ordering': ['-ano_vigencia', 'fracao__nome_abrev'],
                'unique_together': {('fracao', 'ano_vigencia')},
            },
        ),
    ]
