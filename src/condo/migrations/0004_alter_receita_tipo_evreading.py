# Generated by Django 5.2.1 on 2025-05-17 15:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('condo', '0003_alter_despesa_doc'),
    ]

    operations = [
        migrations.AlterField(
            model_name='receita',
            name='tipo',
            field=models.CharField(choices=[('Q', 'Quota'), ('F', 'Fundo comum de reserva'), ('X', 'Quota Extra'), ('O', 'Outros'), ('M', 'Multa'), ('E', 'Eletricidade VE')], max_length=1),
        ),
        migrations.CreateModel(
            name='EVReading',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.DateField(help_text='Data em que a leitura foi tirada (geralmente fim do mês)', verbose_name='Data da Leitura')),
                ('valor', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='<PERSON><PERSON> da Leitura (kWh)')),
                ('foto', models.ImageField(upload_to='ev_readings/%Y/', verbose_name='Foto da Leitura')),
                ('consumo_calculado', models.DecimalField(blank=True, decimal_places=3, help_text='Calculado com base na leitura anterior', max_digits=10, null=True, verbose_name='Consumo Calculado (kWh)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('fracao', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='ev_charges', to='condo.fracao', verbose_name='Fracção')),
                ('receita', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ev_reading', to='condo.receita', verbose_name='Item de Receita Associado')),
            ],
            options={
                'verbose_name': 'Leitura VE',
                'verbose_name_plural': 'Leituras VE',
                'ordering': ['-data'],
                'unique_together': {('fracao', 'data')},
            },
        ),
    ]
