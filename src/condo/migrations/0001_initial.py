# Generated by Django 5.0.6 on 2024-06-29 06:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('empresa', '0003_bancomovimento'),
    ]

    operations = [
        migrations.CreateModel(
            name='Condo',
            fields=[
                ('empresa_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='empresa.empresa')),
            ],
            bases=('empresa.empresa',),
        ),
        migrations.CreateModel(
            name='Despesa',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.DateField()),
                ('numero', models.CharField(max_length=30)),
                ('valor', models.Decimal<PERSON>ield(decimal_places=2, max_digits=10)),
                ('doc', models.FileField(blank=True, null=True, upload_to='despesas/%Y')),
                ('descricao', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['-data'],
            },
        ),
        migrations.CreateModel(
            name='Fracao',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome_abrev', models.CharField(max_length=2)),
                ('nome', models.CharField(max_length=30)),
                ('permilagem', models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name='Orcamento',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ano', models.IntegerField(choices=[(2024, 2024), (2025, 2025)], unique=True)),
                ('fcr', models.IntegerField(default=20, help_text='Valor em % decidido para o fundo comum de reserva')),
            ],
        ),
        migrations.CreateModel(
            name='OrcamentoLinha',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rubrica', models.CharField(max_length=50)),
                ('valor', models.DecimalField(decimal_places=2, max_digits=10)),
            ],
        ),
        migrations.CreateModel(
            name='Receita',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo', models.CharField(choices=[('Q', 'Quota'), ('F', 'Fundo comum de reserva'), ('X', 'Quota Extra'), ('O', 'Outros')], max_length=1)),
                ('data', models.DateField()),
                ('descricao', models.CharField(max_length=100)),
                ('valor', models.DecimalField(decimal_places=2, max_digits=10)),
                ('valor_por_pagar', models.DecimalField(decimal_places=2, max_digits=10)),
            ],
            options={
                'ordering': ['data'],
            },
        ),
        migrations.CreateModel(
            name='Recibo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ano', models.IntegerField()),
                ('numero', models.IntegerField()),
                ('data', models.DateField()),
                ('data_pagamento', models.DateField()),
                ('descricao', models.CharField(max_length=100)),
                ('valor', models.DecimalField(decimal_places=2, max_digits=10)),
                ('enviado', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='ReciboReceita',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('valor', models.DecimalField(decimal_places=2, max_digits=10)),
            ],
        ),
    ]
