# Generated by Django 5.0.6 on 2024-06-29 06:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('condo', '0001_initial'),
        ('terceiro', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='despesa',
            name='terceiro',
            field=models.ForeignKey(limit_choices_to={'tipo': 'F'}, on_delete=django.db.models.deletion.PROTECT, to='terceiro.terceiro'),
        ),
        migrations.AddField(
            model_name='fracao',
            name='condo',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='condo.condo'),
        ),
        migrations.AddField(
            model_name='fracao',
            name='proprietario',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='terceiro.terceiro'),
        ),
        migrations.AddField(
            model_name='orcamento',
            name='condo',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, to='condo.condo'),
        ),
        migrations.AddField(
            model_name='orcamentolinha',
            name='orcamento',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rubricas', to='condo.orcamento'),
        ),
        migrations.AddField(
            model_name='despesa',
            name='rubrica',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='condo.orcamentolinha'),
        ),
        migrations.AddField(
            model_name='receita',
            name='fracao',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='condo.fracao'),
        ),
        migrations.AddField(
            model_name='recibo',
            name='condo',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, to='condo.condo'),
        ),
        migrations.AddField(
            model_name='recibo',
            name='terceiro',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='terceiro.terceiro'),
        ),
        migrations.AddField(
            model_name='reciboreceita',
            name='receita',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='condo.receita'),
        ),
        migrations.AddField(
            model_name='reciboreceita',
            name='recibo',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='condo.recibo'),
        ),
    ]
