from datetime import date


from django.db import models
from django.db.models import Max, F, OuterRef, Subquery, DateField
from django.forms import ValidationError

from empresa.models import Empresa


#
#  M A N A G E R S
#

class ReceitaManager(models.Manager):
   def with_payments(self):
       return self.prefetch_related('reciboreceita_set__recibo')
   
   def with_latest_payment_date(self):
        """
        Annotates Receita objects with the latest payment date.
        """
        max_payment_date_subquery = ReciboReceita.objects.filter(
            receita=OuterRef('pk')
        ).values('receita').annotate(
            max_date=Max('recibo__data_pagamento')
        ).values('max_date')

        return self.annotate(
            latest_payment_date=Subquery(
                max_payment_date_subquery, output_field=DateField(null=True)
            )
        )




#
#  M O D E L S
#

class Condo(Empresa):
    def __str__(self) -> str:
        return f'{self.nome} - {self.nif}'


class Fracao(models.Model):
    condo = models.ForeignKey(Condo, on_delete=models.PROTECT)
    nome_abrev = models.CharField(max_length=2, null=False, blank=False)
    nome = models.CharField(max_length=30, null=False, blank=False)
    proprietario = models.ForeignKey('terceiro.terceiro', on_delete=models.PROTECT)
    permilagem = models.IntegerField(blank=False)

    def __str__(self) -> str:
        return f'{self.nome_abrev} - {self.nome}'

    @property
    def permilagem_percentual(self):
        return self.permilagem / 100

    def morada(self):
        return f'{self.condo.morada} {self.nome}\n{self.condo.cpostal}'


class Orcamento(models.Model):
    condo = models.ForeignKey(Condo, on_delete=models.PROTECT, null=False, blank=False, default=1)
    ano = models.IntegerField(blank=False, choices=((2024,2024),(2025,2025)), unique=True)
    fcr = models.IntegerField(blank=False, default=20, help_text='Valor em % decidido para o fundo comum de reserva')

    def __str__(self) -> str:
        return f'Orçamento {self.ano}'


class OrcamentoLinha(models.Model):
    orcamento = models.ForeignKey(Orcamento, on_delete=models.CASCADE, related_name='rubricas')
    rubrica = models.CharField(max_length=50, null=False, blank=False)
    valor = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f'{self.rubrica} - {self.valor}'



class Despesa(models.Model):
    terceiro = models.ForeignKey('terceiro.terceiro', on_delete=models.PROTECT, null=False, limit_choices_to={"tipo": "F"})
    data = models.DateField(blank=False)
    numero = models.CharField(max_length=30, null=False, blank=False)
    valor = models.DecimalField(max_digits=10, decimal_places=2)
    doc =  models.ForeignKey('docs.doc', on_delete=models.SET_NULL, null=True, blank=True)
    descricao = models.TextField(null=False, blank=True)
    rubrica = models.ForeignKey(OrcamentoLinha, on_delete=models.PROTECT)

    class Meta:
        ordering = ['-data']

    def __str__(self) -> str:
        return f'{self.rubrica.rubrica:15} {self.data} {self.valor:>7.2f}€ {self.terceiro}'


QUOTA_CHOICES = {
    'Q': 'Quota',
    'F': 'Fundo comum de reserva',
    'X': 'Quota Extra',
    'O': 'Outros',
    'M': 'Multa',
    'E': 'Eletricidade VE',
}

class Receita(models.Model):
    objects = ReceitaManager()

    tipo = models.CharField(max_length=1, null=False, blank=False, choices=QUOTA_CHOICES)
    fracao = models.ForeignKey(Fracao, on_delete=models.PROTECT, null=False)
    data = models.DateField(null=False, blank=False)
    descricao = models.CharField(max_length=100, null=False, blank=False)
    valor = models.DecimalField(max_digits=10, decimal_places=2)
    valor_por_pagar = models.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        ordering = ['data']

    def save(self, *args, **kwargs):
        if not self.pk:  # Only set remaining_balance on first save
            self.valor_por_pagar = self.valor
        super().save(*args, **kwargs)

    def __str__(self) -> str:
        return f'{str(self.fracao):20} {self.data} {self.descricao:35} {self.valor:>7.2f}€ {self.valor_por_pagar:>7.2f}€'

    @property
    def valor_pago(self):
        return self.valor - self.valor_por_pagar

    @property
    def descricao_mobile(self):
        ret = self.descricao
        ret = ret.replace('Fundo comum de reserva', 'FCR')
        ret = ret.replace('Eletricidade VE - ', '')
        return ret

    @property
    def payment_timing_status(self):
        if not hasattr(self, 'latest_payment_date'):
            raise AttributeError("Receita object must be annotated with latest_payment_date. "
                                 "Use Receita.objects.with_latest_payment_date() to annotate.")
        if self.valor_por_pagar > 0:
            if self.data.year == date.today().year and self.data.month == date.today().month:
                return 'unpaid_current'
            elif self.data.year == date.today().year and self.data.month > date.today().month:
                return 'unpaid_future'
            return 'unpaid'
        if self.latest_payment_date is None:
            return 'paid_no_payment_date_info'
        if self.latest_payment_date.year < self.data.year or \
           (self.latest_payment_date.year == self.data.year and self.latest_payment_date.month <= self.data.month) or \
           (self.tipo=='E' and self.latest_payment_date.year == self.data.year and self.latest_payment_date.month <= self.data.month+1):
            return 'paid_on_time_or_earlier'
        return 'paid_late'

    @property
    def color(self):
        color_map = {
            'unpaid': 'red',
            'unpaid_current': 'yellow',
            'unpaid_future': 'lightgray',
            'paid_late': 'darkgreen',
            'paid_on_time_or_earlier': 'lightgreen',
            'paid_no_payment_date_info': 'darkgray'
        }
        # payment_status = self.payment_timing_status
        # if payment_status == 'unpaid':
        #     if self.data.year == date.today().year and self.data.month == date.today().month:
        #         return 'yellow'
        #     elif self.data.year == date.today().year and self.data.month > date.today().month:
        #         return 'LightGray'

        return color_map.get(self.payment_timing_status, 'LightGray')



class Recibo(models.Model):
    condo = models.ForeignKey(Condo, on_delete=models.PROTECT, null=False, blank=False, default=1)
    terceiro = models.ForeignKey('terceiro.terceiro', on_delete=models.PROTECT, null=False)
    ano = models.IntegerField()
    numero = models.IntegerField()
    data = models.DateField(null=False, blank=False)
    data_pagamento = models.DateField(null=False, blank=False)
    descricao = models.CharField(max_length=100, null=False, blank=False)
    valor = models.DecimalField(max_digits=10, decimal_places=2)
    enviado = models.BooleanField(default=False)

    def __str__(self):
        return f'{self.ano}/{self.numero:<3} {self.data} {self.valor:>7.2f}€ {self.terceiro}'


class ReciboReceita(models.Model):
    recibo = models.ForeignKey(Recibo, on_delete=models.CASCADE)
    receita = models.ForeignKey(Receita, on_delete=models.CASCADE)
    valor = models.DecimalField(max_digits=10, decimal_places=2)

    # class Meta:
    #     unique_together = ('recibo', 'receita')

    def __str__(self):
        return f'{self.recibo} - {self.receita} - {self.valor}'

    def save(self, *args, **kwargs):
        self.receita.valor_por_pagar -= self.valor
        if self.receita.valor_por_pagar < 0:
            raise ValueError("O Valor a pagar não pode exceder o valor em divida.")
        self.receita.save()
        super().save(*args, **kwargs)



###  EV Charging

class EVReading(models.Model):
    """
    Records a monthly reading for an EV charger counter, including the photo.
    """
    fracao = models.ForeignKey(
        Fracao,
        on_delete=models.PROTECT,
        related_name='ev_charges',
        verbose_name="Fracção"
    )
    data = models.DateField(
        verbose_name="Data da Leitura",
        help_text="Data em que a leitura foi tirada (geralmente fim do mês)"
    )
    valor = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name="Valor da Leitura (kWh)"
    )
    foto = models.ImageField(
        upload_to='ev_readings/%Y/', # Files will be stored in media/ev_readings/YYYY/
        verbose_name="Foto da Leitura"
    )
    consumo_calculado = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        null=True,
        blank=True,
        verbose_name="Consumo Calculado (kWh)",
        help_text="Calculado com base na leitura anterior"
    )
    # calculated_cost = models.DecimalField(
    #     max_digits=10,
    #     decimal_places=2,
    #     null=True,
    #     blank=True,
    #     verbose_name=_("Custo Calculado (€)"), # Or your currency
    #     help_text=_("Custo calculado com base no consumo e tarifa")
    #)
    receita = models.OneToOneField(
        Receita,
        on_delete=models.SET_NULL, # Don't delete reading if receita item is deleted
        null=True,
        blank=True,
        related_name='ev_reading',
        verbose_name="Item de Receita Associado"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Leitura VE"
        verbose_name_plural = "Leituras VE"
        unique_together = ('fracao', 'data')
        ordering = ['-data'] # Default order by date descending

    def __str__(self):
        return f"Leitura VE {self.data}/{self.valor} kWh - Fração {self.fracao}"

    def clean(self):
        # Ensure reading_value is not less than the previous reading value
        previous_reading = EVReading.objects.filter(
            fracao=self.fracao,
            data__lt=self.data
        ).order_by('-data').first()

        if previous_reading and self.valor < previous_reading.valor:
            raise ValidationError(
                f"O valor da leitura não pode ser menor que a leitura anterior {previous_reading.valor} em {previous_reading.data}."
            )

    def save(self, *args, **kwargs):
        # Calculate consumption before saving if reading_value is provided
        if self.valor is not None:
            previous_reading = EVReading.objects.filter(
                fracao=self.fracao,
                data__lt=self.data
            ).order_by('-data').first()

            if previous_reading:
                self.consumo_calculado = self.valor - previous_reading.valor
            else:
                 # no previous reading, so consumption is just the current reading
                 self.consumo_calculado = self.valor

        super().save(*args, **kwargs)


### Seguros das Frações

class SeguroFracao(models.Model):
    fracao = models.ForeignKey(
        Fracao,
        on_delete=models.PROTECT,
        related_name='seguros',
        verbose_name="Fração"
    )
    ano_vigencia = models.IntegerField(
        verbose_name="Ano de Vigência",
        help_text="Ano para o qual o seguro é válido"
    )
    comprovativo = models.FileField(
        upload_to='seguros_fracao/%Y/',
        verbose_name="Comprovativo do Seguro",
        help_text="Documento comprovativo do seguro (e.g., PDF, imagem)"
    )
    data_envio_comprovativo = models.DateField(
        null=True,
        blank=True,
        verbose_name="Data de Envio do Comprovativo",
        help_text="Data em que o proprietário enviou o comprovativo"
    )

    criado_em = models.DateTimeField(auto_now_add=True, verbose_name="Criado em")
    atualizado_em = models.DateTimeField(auto_now=True, verbose_name="Atualizado em")

    class Meta:
        verbose_name = "Seguro da Fração"
        verbose_name_plural = "Seguros das Frações"
        unique_together = ('fracao', 'ano_vigencia')
        ordering = ['-ano_vigencia', 'fracao__nome_abrev']

    def __str__(self):
        return f"Seguro {self.fracao} - Ano {self.ano_vigencia}"

