from django.forms import ModelForm

from .models import <PERSON>pesa, OrcamentoLinha

from terceiro.models import Terceiro


class DespesaForm(ModelForm):
    class Meta:
        model = Despesa
        fields = ['terceiro', 'data', 'valor', 'rubrica', 'numero', 'descricao']

    def __init__(self, *args, **kwargs):
        condo = kwargs.pop('condo')
        ano = kwargs.pop('ano')
        readonly_fields = kwargs.pop('readonly_fields', [])
        super().__init__(*args, **kwargs)
        self.fields['terceiro'].queryset = Terceiro.objects.filter(empresa=condo)
        self.fields['rubrica'].queryset = OrcamentoLinha.objects.filter(orcamento__condo=condo, orcamento__ano=ano)
        for field_name in readonly_fields:
            if field_name in self.fields:
                self.fields[field_name].widget.attrs['readonly'] = True