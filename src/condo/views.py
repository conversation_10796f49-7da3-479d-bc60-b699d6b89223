from datetime import date

from django.shortcuts import render
from django.http import HttpResponse
from django.template.loader import render_to_string
from django import forms
from django.core.mail import EmailMessage
from django.db.models import Sum

from core.email import EmailHtml
from core.utils import generate_html_colors, get_contrast_font_color
from core.htmx import add_trigger
from core.constants import MONTHS

from .models import (
    Condo, Fracao, 
    Orcamento, OrcamentoLinha,
    Despesa, Receita,
    Recibo, ReciboReceita
)
from empresa.models import BancoMovimento
from terceiro.models import Terceiro

from .utils import (
    totals, 
    crosstab_despesas, 
    crosstab_receitas
)
from .forms import DespesaForm



#
#  D E S P E S A S
# 

def new_despesas_ct_view(request):
    ano = 2025
    condo = Condo.objects.get(pk=1)
    
    # despesas normais
    rubricas = OrcamentoLinha.objects.filter(orcamento__condo=condo, orcamento__ano=ano).annotate(executado=Sum("despesa__valor"))
    for rubrica in rubricas:
        rubrica.saldo = rubrica.valor - rubrica.executado
    
    # Multas
    multas = Receita.objects.filter(fracao__condo=condo, data__year=ano, tipo='M').aggregate(valor=Sum("valor"),valor_por_pagar=Sum("valor_por_pagar"))
    multas['executado'] = multas['valor'] - multas['valor_por_pagar']
    multas['saldo'] = multas['valor_por_pagar'] 
    
    # carregamentos VE
    eletricidade = Receita.objects.filter(fracao__condo=condo, data__year=ano, tipo='E').aggregate(valor=Sum("valor"),valor_por_pagar=Sum("valor_por_pagar"))
    eletricidade['executado'] = eletricidade['valor'] - eletricidade['valor_por_pagar']
    eletricidade['saldo'] = eletricidade['valor_por_pagar'] 

    context = {
        "rubricas": rubricas,
        "multas": multas,
        "eletricidade": eletricidade,
    }
    return render(request, 'condo/new_despesas_ct.html', context=context)



def new_despesas_rubrica(request, rubrica_id):
    ano = 2025
    condo = Condo.objects.get(pk=1)

    receitas = None
    despesas = None

    if rubrica_id == 111111:
        receitas = Receita.objects.filter(fracao__condo=condo, data__year=ano, tipo='E')
    elif rubrica_id == 111112:
        receitas = Receita.objects.filter(fracao__condo=condo, data__year=ano, tipo='M')
    else:
        despesas = Despesa.objects.filter(rubrica_id=rubrica_id)
    context = {
        "receitas": receitas.order_by('data') if receitas else [],
        "despesas": despesas.order_by('data') if despesas else [],
    }
    return render(request, 'condo/new_despesas_rubrica.html', context=context)



#
#  R E C E I T A S
#

def new_receitas_ct_view(request):
    ano = 2025
    condo = Condo.objects.get(pk=1)
    today = date.today()

    ct_rceitas = {}
    fraccoes = condo.fracao_set.all().order_by('nome_abrev')

    for fracao in fraccoes:
        ct_rceitas[fracao] = [
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # PREV YEAR
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # JAN
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # FEB
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # MAR
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # APR
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # MAY
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # JUN
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # JUL
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # AUG
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # SEP
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # OCT
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # NOV
            {'valor': 0, 'has_unpaid': False, 'has_paid_on_time_or_earlier': False, 'has_paid_late': False, 'has_no_payment_date_info': False}, # DEC
        ]               

    # Add previous year totals
    receitas = Receita.objects.with_latest_payment_date().filter(fracao__condo=condo, data__year__lt=ano)
    for receita in receitas:
        if receita.valor_por_pagar > 0:
            ct_rceitas[receita.fracao][0]['valor'] += receita.valor_por_pagar
        # Determine the payment timing status
        if receita.latest_payment_date is None:
            continue  # Skip if no payment date info
        if receita.latest_payment_date.year  < ano:
            # If the latest payment date is before the year of interest, we consider it as no payment date info
            continue
        if receita.valor_por_pagar > 0:
            ct_rceitas[receita.fracao][0]['has_unpaid'] = True
        else:
            ct_rceitas[receita.fracao][0]['has_paid_late'] = True

    # current year totals
    receitas = Receita.objects.with_latest_payment_date().filter(fracao__condo=condo, data__year=ano)
    for receita in receitas:
        index = receita.data.month

        if receita.valor_por_pagar > 0:
            ct_rceitas[receita.fracao][index]['valor'] += receita.valor_por_pagar

        match receita.payment_timing_status:
            case 'unpaid':
                ct_rceitas[receita.fracao][index]['has_unpaid'] = True
            case 'paid_late':
                ct_rceitas[receita.fracao][index]['has_paid_late'] = True
            case 'paid_on_time_or_earlier':
                ct_rceitas[receita.fracao][index]['has_paid_on_time_or_earlier'] = True
            case 'no_payment_date_info':
                ct_rceitas[receita.fracao][index]['has_no_payment_date_info'] = True
            case _:
                # This should not happen, but we handle it gracefully
                continue


    # colors for crosstab
    for fracao, values in ct_rceitas.items():
        for i, value in enumerate(values):
            if value['has_unpaid']:
                if ano == date.today().year and i == date.today().month:
                    value['color'] = 'yellow'
                elif ano == date.today().year and i > date.today().month:
                    value['color'] = 'LightGray'
                else:
                    value['color'] = 'red'
            elif value['has_paid_late']:
                value['color'] = 'darkgreen'
            elif value['has_paid_on_time_or_earlier']:
                value['color'] = 'lightgreen'
            elif value['has_no_payment_date_info']:
                value['color'] = 'DarkGray'
            else:
                if i == 0:  # Previous year
                    value['color'] = 'white'
                else:
                    value['color'] = 'LightGray'


    context = {
        "ano": ano,
        "condo": condo,
        "ct_receitas": ct_rceitas,
    }
    return render(request, 'condo/new_receitas_ct.html', context=context)


def new_receita_fracao_view(request, fracao_id):
    qs = Receita.objects.with_latest_payment_date().filter(fracao_id=fracao_id, data__year=2025)

    context = {
        "fracao": Fracao.objects.get(pk=fracao_id),
        "receitas": qs
    }
    return render(request, 'condo/new_receitas_fracao.html', context=context)





#
#  BANCO MOVIMENTOS
#

def banco_movimentos_monthly_view(request):
    condo = Condo.objects.get(pk=1)
    ano = 2025
    
    # Get the selected month or default to current month
    selected_month = request.GET.get('month', date.today().month)
    
    try:
        selected_month = int(selected_month)
    except (ValueError, TypeError):
        selected_month = date.today().month
    
    # Get all movements for the selected month
    movimentos = BancoMovimento.objects.filter(
        empresa=condo,
        data__year=ano,
        data__month=selected_month
    ).order_by('data')
    
    # Add visual indicators without using the same color scheme as payment status
    for movimento in movimentos:
        # Use different styling approach - icons + subtle background colors
        if movimento.valor > 0:
            movimento.movement_type = "entrada"
            movimento.icon = "arrow-down"  # Bootstrap/FontAwesome icon
            movimento.row_class = "movimento-entrada"
        elif movimento.valor < 0:
            movimento.movement_type = "saida"
            movimento.icon = "arrow-up"
            movimento.row_class = "movimento-saida"
        else:
            movimento.movement_type = "neutro"
            movimento.icon = "minus"
            movimento.row_class = "movimento-neutro"
            
        # Add payment status if it's linked to a receipt
        if hasattr(movimento, 'justificacao_doc') and movimento.justificacao_doc:
            if isinstance(movimento.justificacao_doc, Receita):
                movimento.payment_status = movimento.justificacao_doc.payment_timing_status
            else:
                movimento.payment_status = None
        else:
            movimento.payment_status = None
    
    context = {
        "condo": condo,
        "ano": ano,
        "movimentos": movimentos,
        "selected_month": selected_month,
        "selected_month_name": MONTHS[selected_month-1],
        "months": [(i, MONTHS[i-1]) for i in range(1, 13)],
    }
    
    return render(request, 'condo/banco_movimentos_mensal.html', context=context)


def bm_justificacao_view(request, id):
    bm = BancoMovimento.objects.get(pk=id)
    doc = bm.justificacao_doc
    context = {
        "doc": doc,
        "is_recibo": isinstance(doc, Recibo),
        "is_despesa": isinstance(doc, Despesa),
    }
    return render(request, 'condo/bancomovimento_justificacao.html', context=context)
    





def dashboard_view(request):
    ano = 2025
    condo = Condo.objects.get(pk=1)
    context = {
        "condo": condo,
        "ano": ano,
    }
    return render(request, 'condo/dashboard.html', context=context)


def dashboard_totals_view(request):
    ano = 2025
    condo = Condo.objects.get(pk=1)
    context = {
        "condo": condo,
        "ano": ano,
        "totals": totals(condo, ano),
    }
    return render(request, 'condo/dashboard_totals.html', context=context)



def despesas_ct_view(request):
    ano = 2025
    condo = Condo.objects.get(pk=1)
    context = {
        "ct_despesas": crosstab_despesas(condo, ano),
    }
    return render(request, 'condo/despesas_ct.html', context=context)


def receitas_ct_view(request):
    ano = 2025
    condo = Condo.objects.get(pk=1)
    context = {
        'current_month': date.today().month,
        "ct_receitas": crosstab_receitas(condo, ano),
    }
    return render(request, 'condo/receitas_ct.html', context=context)


def bancomovimentos_view(request):
    ano = 2025
    condo = Condo.objects.get(pk=1)
    context = {
        "banco_movimentos": BancoMovimento.objects.filter(empresa=condo).order_by('data')
    }
    return render(request, 'condo/bancomovimentos.html', context=context)


def bancomovimentos_por_justificar_view(request):
    ano = 2025
    condo = Condo.objects.get(pk=1)
    context = {
        "banco_movimentos_por_justificar": BancoMovimento.objects.filter(empresa=condo, descricao__icontains='smas').order_by('data')
    }
    return render(request, 'condo/bancomovimentos_por_justificar.html', context=context)


def despesas_rubrica_view(request, rubrica_id):
    context = {
        "rubrica": OrcamentoLinha.objects.get(pk=rubrica_id),
        "despesas": Despesa.objects.filter(rubrica_id=rubrica_id).order_by('data')
    }
    return render(request, 'condo/despesas_rubrica.html', context=context)



def prepare_receitas_set(qs):
    #colors = ('DarkGrey', 'DarkOliveGreen', 'Crimson', 'Coral', 'CornflowerBlue', 'AliceBlue', 'Aquamarine', 'Black', 'Blanched' 'Almond', 'DarkSlateBlue', 'GreenYellow', 'HotPink')
    colors = generate_html_colors(12)

    qs = qs.select_related('fracao')\
           .prefetch_related('reciboreceita_set__recibo')\
           .order_by('data')

    color_index = 0
    for receita in qs:
        for rr in receita.reciboreceita_set.all():
            if not hasattr(rr.recibo, 'color'):
                color = colors[color_index % 12]
                color_index += 1
                rr.recibo.color = color
                rr.recibo.fontcolor = get_contrast_font_color(color)
    return qs


def receita_fracao_view(request, fracao_id):
    qs = Receita.objects.filter(fracao_id=fracao_id)
    context = {
        "fracao": Fracao.objects.get(pk=fracao_id),
        "receitas": prepare_receitas_set(qs)
    }
    return render(request, 'condo/receitas_fracao.html', context=context)


def receita_terceiro_view(request, terceiro_id):
    qs = Receita.objects.filter(fracao__proprietario_id=terceiro_id)
    context = {
        "terceiro": Terceiro.objects.get(pk=terceiro_id),
        "receitas": prepare_receitas_set(qs)
    }
    return render(request, 'condo/receitas_fracao.html', context=context)


class MultipleFileInput(forms.ClearableFileInput):
    allow_multiple_selected = True


class MultipleFileField(forms.FileField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("widget", MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data, initial=None):
        single_file_clean = super().clean
        if isinstance(data, (list, tuple)):
            result = [single_file_clean(d, initial) for d in data]
        else:
            result = [single_file_clean(data, initial)]
        return result
    

class EmailForm(forms.Form):
    subject = forms.CharField(max_length=100, required=True)
    message = forms.CharField(widget=forms.Textarea, label="Message")
    attachments = MultipleFileField(label="Attachments")


def send_overview_view(request):
    condo = Condo.objects.get(pk=1)
    ano = 2024
    initial = {'subject': f'{condo.nome} - Resumo'}
    form = EmailForm(request.POST or None, request.FILES or None, initial=initial)
    if request.method == 'POST':
        if form.is_valid():
            # Get the form data
            subject = form.cleaned_data['subject']
            message = form.cleaned_data['message']

            # prepare email body
            context ={
                'message': message,
                'current_month': date.today().month,
                'overview': totals(condo, ano),
                'overview_despesas': crosstab_despesas(condo, ano),
                'overview_receitas': crosstab_receitas(condo, ano),
                'movimentos_bancarios': BancoMovimento.objects.filter(empresa=condo, data__year=ano)
            }
            html_msg = render_to_string('condo/email_overview.html', context)
    
            # Create the email
            # bcc = list({t.email for t in Terceiro.objects.filter(fracao__condo=condo)})
            bcc = ['<EMAIL>']
            email = EmailHtml(subject=subject, bcc=bcc)
            email.attach_html(html_msg)
            # Attach files
            for file in form.cleaned_data['attachments']:
                email.attach(file.name, file.read())

            # Send the email
            email.send()

            print("ALERT overview email sent!")
            return HttpResponse("")
    # else:
    #     form = EmailForm()

    return render(request, 'condo/send_overview.html', {'form': form})


def send_email_view(request):
    condo = Condo.objects.get(pk=1)
    ano = 2024
    form = EmailForm(request.POST or None, request.FILES or None)
    if request.method == 'POST':
        if form.is_valid():
            # Get the form data
            subject = form.cleaned_data['subject']
            message = form.cleaned_data['message']
  
            # Create the email
            bcc = list({t.email for t in Terceiro.objects.filter(fracao__condo=condo)})
            #bcc = ['<EMAIL>']
            email = EmailMessage(
                subject=subject,
                body=message,
                bcc=bcc)
            # Attach files
            for file in form.cleaned_data['attachments']:
                email.attach(file.name, file.read())

            # Send the email
            email.send()

            print("ALERT email sent!")
            return HttpResponse("")

    return render(request, 'condo/send_email.html', {'form': form})


def despesa_from_bancomovimento_view(request, bm_id):
    print('Despesa from banco movimento', bm_id)
    condo = Condo.objects.get(pk=1)
    ano = 2024

    if request.method == 'POST':
        print(request.POST)
        return HttpResponse('')

    bm = BancoMovimento.objects.get(pk=bm_id)
    initial = {
        'data': bm.data,
        'valor': bm.valor,
    }

    # guess from last action
    last_bm = BancoMovimento.objects.filter(descricao=bm.descricao).last()
    if last_bm:
        last_doc = last_bm.justificacao_doc
        if last_doc and  isinstance(last_doc, Despesa):
            initial.update({
                'terceiro':last_doc.terceiro,
                'rubrica': last_doc.rubrica,
            })

    form = DespesaForm(request.POST or None, condo=condo, ano=ano, initial=initial, readonly_fields=['data', 'valor'])

    context = {
        'bm': bm,
        'form': form
    }
    # add_trigger(request, 'despesa')

    return render(request, 'condo/despesa_from_bancomovimento.html', context)


#
#  NEW VIEWS
#

