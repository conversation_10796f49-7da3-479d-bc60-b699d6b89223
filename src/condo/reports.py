from reports.reports import Report, Col, Group

from .models import ReciboReceita


class ReciboResumoReport(Report):
    qs = (ReciboReceita.objects
        .select_related('recibo__terceiro', 'receita__fracao')
        .order_by('recibo__terceiro__nome','recibo__terceiro_id','recibo__ano','recibo__numero','receita__fracao__nome_abrev','receita__data')
    )

    groups = [
        Group(exp='{row.recibo.terceiro.id}', caption='{row.recibo.terceiro}', print_on_new_page=True, footer=True),
        # Group(exp='{row.recibo.id}', caption='{row.recibo.ano}/{row.recibo.numero}', footer=True)
    ]

    cols = [
        Col(w=15, caption='Recibo', exp='{row.recibo.ano}/{row.recibo.numero}', hide_repeated=True),  
        Col(w=17, caption='',       exp='{row.recibo.data_pagamento}', hide_repeated=True),  
        Col(w=17, caption='Data',         exp='{row.receita.data}'),
        Col(w=30, caption='Fração',       exp='{row.receita.fracao.nome_abrev} - {row.receita.fracao.nome}'),
        Col(w=50, caption='Descrição',    exp='{row.receita.descricao}'),
        Col(w=20, caption='Valor',        exp='{row.receita.valor}', align='R'),
        Col(w=20, caption='Valor p/Pagar', exp='{row.receita.valor_por_pagar}', align='R', hide_zero=True),
        Col(w=0,  caption='Valor Pago',   exp='{row.valor}', align='R', sum=True)
    ]





""" class ReciboResumoReport(Report):
    qs = (ReciboReceita.objects
        .select_related('recibo__terceiro', 'receita__fracao')
        .order_by('recibo__terceiro__nome','recibo__terceiro_id','recibo__ano','recibo__numero','receita__fracao__nome_abrev','receita__data')
    )

    @attribs(expression='recibo__terceiro__id', print_on_new_page=True)
    def group_header1(self, row):
        self.cell(0,4,f'{row.recibo.terceiro}', border=1, fill=True )
        self.ln(4)
        #self.data_header()

    # def group_footer1(self, row):
    #     self.cell(0,4,'gf1')
    #     self.ln(8)

    @attribs(expression='recibo__id')
    def group_header2(self, row):
        self.cell(15,4,f'{row.recibo.ano}/{self.row.recibo.numero}', border='B')
        self.cell(17,4,f'{row.recibo.data}', border='B')

    def group_footer2(self, row):
        self.cell(0,4,'gf2', align='R')
        self.ln(4)


    def data_header(self):
        self.cell(32,4, 'Recibo', border=1, fill=True)
        self.cell(17,4, 'data', border=1, fill=True)
        self.cell(30,4, 'fração', border=1, fill=True)
        self.cell(50,4, 'descricao', border=1, fill=True)
        self.cell(20,4, 'valor', border=1, align='R', fill=True)
        self.cell(20,4, 'valor por pagar', border=1, align='R', fill=True)
        self.cell(0,4,  'valor pago', border=1, align='R', fill=True)
        self.ln(6)

    def data(self, row):
        if self.x == self.l_margin:
            self.cell(32,4)  # spacing
        self.cell(17,4, f'{row.receita.data}', border='B')
        self.cell(30,4, f'{row.receita.fracao.nome_abrev} - {row.receita.fracao.nome}', border='B')
        self.cell(50,4, f'{row.receita.descricao}', border='B')
        self.cell(20,4, f'{row.receita.valor}', border='B', align='R')
        self.cell(20,4, f'{row.receita.valor_por_pagar}', border='B', align='R')
        self.cell(0,4, f'{row.valor}', border='B', align='R')
        self.ln(4)





class ReciboReport(Report):
    qs = ReciboReceita.objects.select_related('recibo__terceiro', 'receita__fracao').order_by('recibo__ano','recibo__numero','receita__data')
    page_header = False
    page_footer = False

    @attribs(expression='recibo_id', h=100, force_new_page=True)
    def group_header1(self, row):
        self.image("laranjeiras.jpg", h=15, w=40) #, keep_aspect_ratio=True)
        self.ln(2)

        self.multi_cell(95,text='**Condomínio das Laranjeiras**\nRua António Araújo nº 77\n4580-045 Paredes\n901491950', markdown=True, new_y='TOP')
        self.multi_cell(95,text=f'**RECIBO {row.recibo.ano}/{row.recibo.numero}**\ndata: {row.recibo.data}\nprocessado por computador',markdown=True, align='R')
        self.ln(8)

        self.cell(110)
        self.multi_cell(0,text=f'Exmo(a) Sr(a):\n**{row.recibo.terceiro.nome}\n**{row.recibo.terceiro.morada}\n{row.recibo.terceiro.cpostal_alt}\nnif:{row.recibo.terceiro.nif}', markdown=True)
        self.ln(4)
        self.cell(0,4,f'**Na data: {row.recibo.data_pagamento}**', markdown=True)
        self.ln(4)

        self.cell(0,4,'**recebemos os seguintes valores**', markdown=True)
        self.ln(8)

        self.cell(20,4, 'Data', border=1)
        self.cell(30,4, 'Fração', border=1)
        self.cell(65,4, 'Descrição', border=1)
        self.cell(25,4, 'Valor', border=1, align='R')
        self.cell(25,4, 'Valor por pagar', border=1, align='R')
        self.cell(25,4, 'VALOR PAGO', border=1, align='R')
        self.ln(4)

    def group_footer1(self, row ):
        self.cell(0,4,text='Hello from footer')
        self.ln(4)

    def data(self, row):
        self.cell(20,4, str(row.receita.data), border=1)
        self.cell(30,4, f'{row.receita.fracao.nome_abrev} - {row.receita.fracao.nome}', border=1)
        self.cell(65,4, row.receita.descricao, border=1)
        self.cell(25,4, str(row.receita.valor), border=1, align='R')
        self.cell(25,4, str(row.receita.valor_por_pagar), border=1, align='R')
        self.cell(25,4, str(row.valor), border=1, align='R')
        self.ln(4)
 """