<div class="pure-g">
    <div class="pure-u-1 l-box">

    <div class="bank-movements-container" hx-target="this"  hx-swap="outerHTML">
        <h3>Movimentos Bancários - {{ selected_month_name }} de {{ ano }}</h3>
        
        <select name="month" hx-get="{% url 'condo:banco_movimentos_monthly' %}" style="width: 50%; margin-bottom: 2px;">
            {% for month_num, month_name in months %}
                <option value="{{ month_num }}" {% if month_num == selected_month %}selected{% endif %}>
                    {{ month_name }}
                </option>
            {% endfor %}
        </select>

    <table class="pmtable pmtable-hover">
        <thead>
        <tr>
            <th>Dia</th>
            <th>Descrição</th>
            <th style="text-align:right;">Valor</th>
        </tr>
        </thead>
        <tbody>
        {% for bm in movimentos %}
        <tr>
            <td class="date" rowspan="2">{{bm.data|date:"M d"}}</td>
            <td style="font-weight: bold;">{{bm.descricao}}</td>
            <td style="font-weight: bold; text-align:right;">{{bm.valor}}</td>
        </tr>
        <tr>
            <td colspan="3">
                {% if bm.justificacao_doc %}
                <span 
                    hx-get="{% url 'condo:bm_justificacao_view' bm.id %}" 
                    hx-target="#modal-content"
                    hx-swap="innerHTML"
                    ht-trigger="click"
                    hx-on::after-request="openModal()">
                    {{bm.justificacao}}
                </span>
                {% else %}
                {{bm.justificacao}}
                {% endif %}

            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4">Não existem movimentos para este mês</td>
        </tr>
        {% endfor %}
        </tbody>
    </table>    

    </div>

    </div>
</div>