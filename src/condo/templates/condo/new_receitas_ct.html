<div class="pure-g">
    <div class="pure-u-1 l-box">

    <h3>Receitas</h3>
    <table class="pmtable pmtable-hover">
        <tr>
            <th>Fração</th>
            <th><span style="width: 25px; height: 25px;">{{ano}}</span></th>
            <th><span style="width: 25px; height: 25px;"> J </span>
            <th><span style="width: 25px; height: 25px;"> F </span>
            <th><span style="width: 25px; height: 25px;"> M </span></th>
            <th><span style="width: 25px; height: 25px;"> A </span></th>
            <th><span style="width: 25px; height: 25px;"> M </span></th>
            <th><span style="width: 25px; height: 25px;"> J </span></th>
            <th><span style="width: 25px; height: 25px;"> J </span></th>
            <th><span style="width: 25px; height: 25px;"> A </span></th>
            <th><span style="width: 25px; height: 25px;"> S </span></th>
            <th><span style="width: 25px; height: 25px;"> O </span></th>
            <th><span style="width: 25px; height: 25px;"> N </span></th>
            <th><span style="width: 25px; height: 25px;"> D </span></th>
        </tr>
        {% for fracao, lista in ct_receitas.items %}
        <tr>
            <td style="background-color: lightgray;"
                hx-get="condo/new_receitas_fracao/{{fracao.id}}"
                hx-target="#receitas_resumo"
                hx-swap="innerHtml">
                {{fracao.nome_abrev}} {{fracao.nome}}
            </td>
            {% for v in lista %}
                <td><span style="border-radius: 5px; background-color: {{v.color}}; color:{{v.color}}; width: 25px; height: 25px;">....</span></td>
            {% endfor %}
        </tr>
        {% endfor %}
    </table>
    <div id="receitas_resumo" style="padding-left: 20px; padding-top: 5px;"></div>
    <div>
    <small>
    <span style="border-radius: 5px; background-color: lightgreen; color:lightgreen; width: 25px; height: 25px;">....</span> Pago dentro do mês <br/>
    <span style="border-radius: 5px; background-color: darkgreen; color:darkgreen; width: 25px; height: 25px;">....</span> Pago com atraso <br/>
    <span style="border-radius: 5px; background-color: yellow; color:yellow; width: 25px; height: 25px;">....</span> A pagamento<br/>
    <span style="border-radius: 5px; background-color: red; color:red; width: 25px; height: 25px;">....</span> Não pago<br/>
    <span style="border-radius: 5px; background-color: lightgray; color: lightgray; width: 25px; height: 25px;">....</span> Pagamento ainda não devido<br/>
    </small>
    </div>

    </div>
</div>