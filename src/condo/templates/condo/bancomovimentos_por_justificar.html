<h3>Movimentos Bancários</h3>
<table class="pmtable pmtable-hover">
    <thead>
	<tr>
        <th>Data</th>
        <th>Descrição</th>
        <th style="text-align:right;">Valor</th>
        <th style="text-align:right;">Saldo</th>
        <th>Justificação</th>
    </tr>
    </thead>
    <tbody>
    {% for bm in banco_movimentos_por_justificar %}
    <tr>
        <td class="date">{{bm.data|date:"M d"}}</td>
        <td>{{bm.descricao}}</td>
        <td style="text-align:right;">{{bm.valor}}</td>
        <td style="text-align:right;">{{bm.saldo_contabilistico}}</td>
        {% if forloop.counter == 2 %}
        <td hx-target="this">
            <button class="pure-button pure-button-primary" hx-get="{% url 'condo:despesa_from_bancomovimento' bm.id %}">Despesa</button>
        </td>
        {% else %}
        <td>{{bm.justificacao}}</td>
        {% endif %}
    </tr>
    {% endfor %}
    </tbody>
</table>