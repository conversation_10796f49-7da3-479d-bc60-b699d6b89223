<div>
    <div hx-on:click="this.parentElement.remove();">
        <b>{{fracao}} <small>({{fracao.permilagem_percentual|floatformat:2}})</small></b><br>
        <small>{{fracao.proprietario.nome}}</small>
    </div>
    <table class="pmtable pmtable-hover">
        <tr>
            <th>Data</th>
            <th>Descrição</th>
            <th>Valor</th>
            <th>Por pagar</th>
            <th>Pag.</th>
            <th></th>
        </tr>
        {% for receita in receitas %}
            <tr>
                <td class="date">{{receita.data|date:"M d"}}</td>
                <td>{{receita.descricao_mobile}}</td>
                <td class="money">{{receita.valor}}</td>
                <td class="money">{{receita.valor_por_pagar}}</td>
                <td>{{receita.latest_payment_date|date:"M d"}}</td>
                <td><span style="border-radius: 5px; background-color: {{receita.color}}; color: {{receita.color}}; width: 25px; height: 25px;">....</span></td>
            </tr>
        {% endfor %}
    </table>
</div>