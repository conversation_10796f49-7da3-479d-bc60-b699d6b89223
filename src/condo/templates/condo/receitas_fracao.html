<b>{{fracao}}{{terceiro}}</b>
<table class="pmtable pmtable-hover" style="width: 80%;">
    <tr>
        <th>Data</th>
        <th>Descrição</th>
        <th>Valor</th>
        <th>Por pagar</th>
        <th style="width: 30%;">Documento</th>
    </tr>
    {% for receita in receitas %}
        <tr>
            <td class="date">{{receita.data|date:"M d"}}</td>
            <td>{{receita.descricao}}</td>
            <td class="money">{{receita.valor}}</td>
            <td class="money {% if receita.valor_por_pagar > 0 %}moneyalert{% endif %}">{{receita.valor_por_pagar}}</td>
            <td>
                {% for rr in receita.reciboreceita_set.all %}
                <span style="background-color: {{rr.recibo.color}}; color:{{rr.recibo.fontcolor}};  border-radius: 5px;">{{rr.recibo.data_pagamento|date:"M d"}} - {{rr.valor}}</span>
                {% endfor %}
            </td>
        </tr>
    {% endfor %}
</table>