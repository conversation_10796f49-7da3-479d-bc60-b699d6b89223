<h3>Receitas</h3>
<table class="pmtable pmtable-hover">
    <tr>
        <th>Fração</th>
        <th style="text-align: right;">pre 2024</th>
        <th style="text-align: right;">Jan</th>
        <th style="text-align: right;">Fev</th>
        <th style="text-align: right;">Mar</th>
        <th style="text-align: right;">Abr</th>
        <th style="text-align: right;">Mai</th>
        <th style="text-align: right;">Jun</th>
        <th style="text-align: right;">Jul</th>
        <th style="text-align: right;">Ago</th>
        <th style="text-align: right;">Set</th>
        <th style="text-align: right;">Out</th>
        <th style="text-align: right;">Nov</th>
        <th style="text-align: right;">Dez</th>
        <th><PERSON>priet<PERSON><PERSON></th>
    </tr>
    {% for fracao, lista in ct_receitas.items %}
    <tr>
        <td style="background-color: lightgray;"
            hx-get="condo/receitas_fracao/{{fracao.id}}"
            hx-target="#receitas_resumo"
            hx-swap="innerHtml">
            {{fracao.nome_abrev}} {{fracao.nome}}
        </td>
        {% for v in lista %}
        {% if forloop.counter0 <= current_month and v > 0 %}            
        <td class="money moneyalert">{{v|floatformat:2}}</td>
        {% else %}
        <td class="money">{{v|floatformat:2}}</td>
        {% endif %}
        {% endfor %}
        <td hx-get="condo/receitas_terceiro/{{fracao.proprietario_id}}"
            hx-target="#receitas_resumo"
            hx-swap="innerHtml">
            {{fracao.proprietario}}
        </td>
    </tr>
    {% endfor %}
</table>

<div id="receitas_resumo" style="padding-left: 30px; padding-top: 5px;">

</div>