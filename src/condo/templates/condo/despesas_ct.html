<h3>Despesas</h3>
<table class="pmtable pmtable-hover">
    <tr>
        <th>Rubrica</th>
        <th style="text-align: right;">Orçamento</th>
        <th style="text-align: right;">Total</th>
        <th style="text-align: right;">Janeiro</th>
        <th style="text-align: right;">Fevereiro</th>
        <th style="text-align: right;">Março</th>
        <th style="text-align: right;">Abril</th>
        <th style="text-align: right;">Maio</th>
        <th style="text-align: right;">Junnho</th>
        <th style="text-align: right;">Julho</th>
        <th style="text-align: right;">Agosto</th>
        <th style="text-align: right;">Setembro</th>
        <th style="text-align: right;">Outubro</th>
        <th style="text-align: right;">Novembro</th>
        <th style="text-align: right;">Dezembro</th>
    </tr>
    {% for rubrica, lista in ct_despesas.items %}
    <tr>
        <td style="background-color: lightgray;" 
            hx-get="condo/despesas_rubrica/{{rubrica.id}}"
            hx-target="#despesas_rubrica" 
            hx-swap="innerHtml">{{rubrica.rubrica}}
        </td>
        <td style="text-align: right;">{{rubrica.valor}}</td>
        {% for v in lista %}
            {%if forloop.counter0 == 0 and v > rubrica.valor %}
            <td class="money moneyalert">{{v|floatformat:2}}</td>
            {% else %}
            <td class="money">{{v|floatformat:2}}</td>
            {% endif %}
        {% endfor %}
    </tr>
    {% endfor %}
</table>
<div id="despesas_rubrica" style="padding-left: 30px; padding-top: 5px;">

</div>
