<div hx-target="this">
    <p>
    Para além da situação calculada à data pode ainda escrever texto e adicionar ficheiros que serão enviados em anexo.
    Por exemple adicionar extrato bancário, faturas de obras, etc.
    </p>
    <form hx-post="{% url 'condo:send_overview' %}" hx-encoding='multipart/form-data' hx-disabled-elt="find button">
        {{form.as_p}}
        <button class="pure-button pure-button-primary" style="width: 100%;">Enviar overview email</button>
    </form>
</div>