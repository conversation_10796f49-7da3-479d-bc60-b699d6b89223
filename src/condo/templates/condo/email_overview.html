<html>
    <body>
        <p>Exmos Condóminos<br/>Esta é a posição geral da gestão corrente do condominio.</p>
        <p>{{message}}</p>
        <h3>Resumo</h3>
        <table style="border-collapse: collapse;">
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc; text-align: left;">Orçamento</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.valor_orcamento|floatformat:2}}€</td>
            </tr>
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc; text-align: left;">Despesas pagas</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.despesas_pagas|floatformat:2}}€<br/>{{overview.percent_orcamento|floatformat:2}}%</td>
            </tr>
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc; text-align: left;">Folga</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.orcamento_folga|floatformat:2}}€</td>
            </tr>
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc; text-align: left;">Despesas pagas sem Orçamento</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.despesas_pagas_sem_orcamento|floatformat:2}}€</td>
            </tr>
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc; text-align: left;">FCR</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.fcr|floatformat:2}}€</td>
            </tr>
        </table>
        <br/>
        <table style="border-collapse: collapse;">
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc; text-align: left;">Valor previsto pre 2024</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.valor_previsto_pre2024|floatformat:2}}€</td>
            </tr>
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc; text-align: left;">Valor recebido pre 2024</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.valor_recebido_pre2024|floatformat:2}}€</td>
            </tr>
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc; text-align: left;">Valor a rceber pre 2024</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.valor_a_receber_pre2024|floatformat:2}}€</td>
            </tr>
        </table>
        <br/>
        <table style="border-collapse: collapse;">
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc ;text-align: left;">Valor previsto</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.valor_previsto|floatformat:2}}€</td>
            </tr>
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc; text-align: left;">Valor recebidos</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.valor_recebido|floatformat:2}}€</td>
            </tr>
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc; text-align: left;">Valor a receber</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.valor_a_receber|floatformat:2}}€</td>
            </tr>
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; background-color: #ccc; text-align: left;">Valor em atraso</th>
                <td style="border: 1px solid black; padding: 2px 5px; text-align: right;">{{overview.valor_em_atraso|floatformat:2}}€</td>
            </tr>
        </table>

        <h3>Despesas</h3>
        <table style="border-collapse: collapse;">
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Rubrica</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Orçamento</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Total</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Janeiro</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Fevereiro</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Março</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Abril</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Maio</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Junnho</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Julho</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Agosto</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Setembro</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Outubro</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Novembro</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Dezembro</th>
            </tr>
            {% for rubrica, lista in overview_despesas.items %}
            <tr>
                <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%;">{{rubrica.rubrica}}</td>
                <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%; text-align: right;">{{rubrica.valor}}</td>
                {% for v in lista %}
                {%if forloop.counter0 == 0 and v > rubrica.valor %}
                <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%; text-align: right;background-color: red; color: white;">{{v|floatformat:2}}</td>
                {% else %}
                <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%; text-align: right;">{{v|floatformat:2}}</td>
                {% endif %}
                {% endfor %}
            </tr>
            {% endfor %}
        </table>
        <p>Controlo do Orçamento valores orçamentados/ valores liquidados totais e desagregados por mês</p>


        <h3>Receitas</h3>
        <table style="border-collapse: collapse; width: 100%;">
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Fração</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">pre 2024</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Jan</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Fev</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Mar</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Abr</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Mai</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Jun</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Jul</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Ago</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Set</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Out</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Nov</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Dez</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Proprietário</th>
            </tr>
            {% for fracao, lista in overview_receitas.items %}
            <tr>
                <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%;">{{fracao.nome_abrev}} {{fracao.nome}}</td>
                {% for v in lista %}
                {% if forloop.counter0 <= current_month and v > 0 %}            
                <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%; text-align: right; background-color: red; color: white;">{{v|floatformat:2}}</td>
                {% else %}
                <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%; text-align: right;">{{v|floatformat:2}}</td>
                {% endif %}
                {% endfor %}
                <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%;">{{fracao.proprietario}}</td>
            </tr>
            {% endfor %}
        </table>
        <p>Os valores indicam os montantes em falta. Valores assinalados a vermelho indicam falta de pagamento e data ultrapassada</p>

        <h3>Movimentos Bancários</h3>
        <table style="border-collapse: collapse;">
            <tr>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Data</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Descrição</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Valor</th>
                <th style="border: 1px solid black; padding: 2px 5px; font-size: 80%; background-color: #ccc;">Justificação</th>
            </tr>
            {% for mov in movimentos_bancarios %}
                <tr>
                    <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%;">{{mov.data|date:"d.m.Y"}}</td>
                    <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%;">{{mov.descricao}}</td>
                    <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%; text-align: right;">{{mov.valor}}€</td>
                    <td style="border: 1px solid black; padding: 2px 5px; font-size: 80%;">{{mov.justificacao}}</td>
                </tr>         
            {% endfor %}
        </table>
        {% include 'condo/footer_contactos.html'%}
    </body>
</html>        