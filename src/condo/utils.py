from datetime import date
import glob

from django.db.models import Sum, Max, F, OuterRef, Subquery, Case, When, Value, Q, DateField, CharField
from django.db.models.functions import ExtractYear, ExtractMonth

from .models import *


def totals(condo, year):
    orcamento = Orcamento.objects.get(condo=condo, ano=year)

    # date
    today = date.today()
    day_of_year = (today - date(today.year,1,1)).days +1
    percent_year = (day_of_year / 365) * 100

    # execucao orcamento
    valor_orcamento = orcamento.rubricas.aggregate(total=Sum('valor', default=0))['total']
    despesas_pagas = Despesa.objects.filter(rubrica__orcamento=orcamento, rubrica__valor__gt=0).aggregate(total=Sum("valor", default=0))["total"]
    percent_orcamento = despesas_pagas / valor_orcamento * 100
    orcamento_folga = valor_orcamento-despesas_pagas    
    fcr = valor_orcamento * orcamento.fcr / 100
    despesas_pagas_sem_orcamento = Despesa.objects.filter(rubrica__orcamento=orcamento, rubrica__valor=0).aggregate(total=Sum("valor", default=0))["total"]

    qs = Receita.objects.filter(fracao__condo=condo, data__year=year)
    valor_previsto = qs.aggregate(total=Sum('valor', default=0))['total']
    valor_recebido = qs.aggregate(total=Sum(F("valor")-F("valor_por_pagar"), default=0))["total"]
    valor_a_receber = qs.aggregate(total=Sum("valor_por_pagar", default=0))["total"]
    valor_em_atraso = qs.filter(data__lte=today).aggregate(total=Sum("valor_por_pagar", default=0))["total"]

    qs = Receita.objects.filter(fracao__condo=condo, data__year__lt=year)
    valor_previsto_pre2024 = qs.aggregate(total=Sum('valor', default=0))['total']
    valor_recebido_pre2024 = qs.aggregate(total=Sum(F("valor")-F("valor_por_pagar"), default=0))["total"] 
    valor_a_receber_pre2024 = valor_previsto_pre2024 - valor_recebido_pre2024

    return locals()


def crosstab_despesas(condo, year):
    orcamento = Orcamento.objects.get(condo=condo, ano=year)

    ct_despesas = {}
    rubricas = OrcamentoLinha.objects.filter(orcamento=orcamento)
    for rubrica in rubricas:
        ct_despesas[rubrica] = [0,0,0,0,0,0,0,0,0,0,0,0,0]
    despesas = Despesa.objects.filter(rubrica__orcamento=orcamento)
    for despesa in despesas:
        ct_despesas[despesa.rubrica][despesa.data.month] += despesa.valor
        ct_despesas[despesa.rubrica][0] += despesa.valor

    return ct_despesas


def crosstab_receitas(condo, year):
    ct_receitas = {}
    fracoes = (condo.fracao_set.all()
                .select_related('proprietario')
                .order_by('nome_abrev'))
    for fracao in fracoes:
        ct_receitas[fracao] = [0,0,0,0,0,0,0,0,0,0,0,0,0]

    receitas = Receita.objects.filter(
        fracao__condo=condo,
        valor_por_pagar__gt=0,
        #data__lte=datetime.now()
    )
    for receita in receitas:
        index = receita.data.month
        if receita.data.year < year:
            index = 0
        ct_receitas[receita.fracao][index] += receita.valor_por_pagar

    return ct_receitas






def get_receitas_with_payment_timing():
    """
    Returns a queryset of all Receita objects, annotated with their
    payment timing status ('unpaid', 'paid_on_time_or_earlier', 'paid_late',
    or 'paid_no_payment_date_info').
    """

    # Subquery to find the maximum (latest) payment date for each Receita.
    # This looks at all ReciboReceita entries for a given Receita
    # and finds the latest 'data_pagamento' from the associated Recibo.
    max_payment_date_subquery = ReciboReceita.objects.filter(
        receita=OuterRef('pk')  # Link to the outer Receita query
    ).values(
        'receita'  # Group by receita (though OuterRef handles the link)
    ).annotate(
        max_date=Max('recibo__data_pagamento')  # Get the latest payment date
    ).values(
        'max_date'  # Select only this latest date
    )

    receitas_queryset = Receita.objects.filter(data__year=2025).annotate(
        # Annotate each Receita with its latest payment date.
        # output_field is DateField, allowing null if no payment date is found.
        latest_payment_date=Subquery(max_payment_date_subquery, output_field=DateField(null=True))
    ).annotate(
        # Now, categorize the payment timing based on 'latest_payment_date'
        # and 'Receita.data'.
        payment_timing_status=Case(
            # 1. If 'valor_por_pagar' is greater than 0, it's 'unpaid'.
            When(valor_por_pagar__gt=0, then=Value('unpaid')),

            # 2. If fully paid but no 'latest_payment_date' (e.g., data issue or manual update),
            #    mark as 'paid_no_payment_date_info'.
            When(latest_payment_date__isnull=True, then=Value('paid_no_payment_date_info')),

            # 3. If paid on time or earlier:
            #    - Payment year is before Receita.data year, OR
            #    - Payment year is the same, and payment month is <= Receita.data month.
            When( # Corrected condition
                Q(latest_payment_date__year__lt=F('data__year')) |
                (
                    Q(latest_payment_date__year=F('data__year')) &
                    Q(latest_payment_date__month__lte=F('data__month'))
                ),
                then=Value('paid_on_time_or_earlier')
            ),

            # 4. Default: If fully paid but doesn't meet 'on_time_or_earlier' criteria,
            #    it's 'paid_late'.
            default=Value('paid_late'),
            output_field=CharField()
        )
    )
    return receitas_queryset
