import glob
from pathlib import Path
from datetime import datetime, date, timedelta
from decimal import Decimal
import json
from collections import defaultdict
import io
import sys

from prompt_toolkit import prompt
from prompt_toolkit.completion import WordCompleter
from prompt_toolkit.validation import Validator, ValidationError

from django.core.management.base import BaseCommand
from django.db.models import Sum, F, Prefetch
from django.db import transaction
from django.template.loader import render_to_string
from django.utils import timezone


from core.constants import MONTHS
from core.email import EmailHtml
from empresa.models import DocNumber
from terceiro.models import Terceiro
from condo.models import (
    Condo, Orcamento, OrcamentoLinha, Receita, Despesa,
    Recibo, ReciboReceita
)
from condo.process import aplicar_orcamento, enviar_avisos
from empresa.models import BancoMovimento
from empresa.process import bancomovimento_import_csv
from empresa.utils import ETCodeQR
from docs.process import fetch_and_save_emails
from docs.models import Doc

from condo.utils import totals, crosstab_despesas, crosstab_receitas


class DecimalValidator(Validator):
    def validate(self, document):
        text = document.text
        try:
            Decimal(text)
        except Exception:
            raise ValidationError(message='Non decimal number',cursor_position=len(text))

class DateValidator(Validator):
    def validate(self, document):
        text = document.text
        try:
            datetime.strptime(text, '%Y-%m-%d')
        except Exception:
            raise ValidationError(message='Not a valid date format yyyy-mm-dd',cursor_position=len(text))            

class WordValidator(Validator):
    def __init__(self, name, valid_words):
        self.name = name
        self.valid_words = valid_words

    def validate(self, document):
        text = document.text
        if text.lower() not in [word.lower() for word in self.valid_words]:
            raise ValidationError(message=f'Invalid input. Please select a valid {self.name}.', cursor_position=len(text))



class Command(BaseCommand):
    help = """UI for Condo app"""

    def handle(self, *args, **options):
        self.current_condo = Condo.objects.get(nif='901491950')
        self.current_year = 2025
        
        self.orcamento = Orcamento.objects.get(condo=self.current_condo, ano=self.current_year)

        print("=" * 80)
        print("Condominio: ", self.current_condo)
        print("ano: ", self.current_year)
        print("=" * 80)

        # self.hints = {}
        # hints_path = Path('hints.json')
        # if hints_path.exists():
        #     with hints_path.open('r') as f:
        #         self.hints = json.load(f)

        print("\nprocessing email...")
        fetch_and_save_emails()
        
        print("\nprocessing bank files...")
        files = glob.glob('/home/<USER>/Downloads/Net24_MovConta_242100025947_*.xls')
        files.sort()
        for file_name in files:
            print(f"\t{file_name}")
            bancomovimento_import_csv(self.current_condo, Path(file_name))
        
        qs = BancoMovimento.objects.filter(empresa=self.current_condo)
        print(f'\nMovimentos bancários {qs.count()}/{qs.filter(justificacao='').count()} por justificar')

        self.overview()

        qs = BancoMovimento.objects.filter(empresa=self.current_condo, justificacao='')
        if qs.exists():
            print("\nM O V I M E N T O S    P O R    J U S T I F I C A R\n")
            for mov in qs:
                print(mov)

        # build completers/validators
        self.date_validator = DateValidator()
        self.decimal_validator = DecimalValidator() 
        fornecedores_nomes =  [t.nome for t in Terceiro.objects.filter(empresa=self.current_condo, tipo='F')]
        proprietarios_nomes = [t.nome for t in Terceiro.objects.filter(empresa=self.current_condo, tipo='P')]
        rubricas_nomes =      [r.rubrica for r in self.orcamento.rubricas.all()]
        self.fornecedor_completer   = WordCompleter(fornecedores_nomes, ignore_case=True)
        self.fornecedor_validator   = WordValidator('Fornecedor', fornecedores_nomes)
        self.proprietario_completer = WordCompleter(proprietarios_nomes, ignore_case=True)
        self.proprietario_validator = WordValidator('Proprietario', proprietarios_nomes)
        self.rubrica_completer      = WordCompleter(rubricas_nomes, ignore_case=True)
        self.rubrica_validator      = WordValidator('Rubrica', rubricas_nomes)

        while True:
            # print top menu
            print("\n===     M E N U  ===\n")
            print("1 - Justicar movimentos bancários")
            print('\nDespesas')
            print("11 - Associar docs de emails a despesas")
            #print("11 - Registar despesa")
            print('\nReceitas')
            print("21 - Enviar avisos")
            print("22 - Enviar recibos")
            print("\nutils")
            print("91 - Aplicar orçamento")
            print("99 - Enviar overview")
            option = input('\n> ')

            match option:
                case '0':
                    break
                case '1':
                    self.justificar_movimentos()
                case '11':
                    self.despesa_fillin_doc()
                case '21':
                    self.enviar_avisos()
                case '22':
                    self.enviar_recibos()
                case '91':
                    aplicar_orcamento(self.current_condo, self.current_year)
                case '99':
                    self.enviar_overview()

        # with hints_path.open('w') as f:
        #     json.dump(self.hints, f)


    def get_terceiro_from_bancomov(self, mov:BancoMovimento):
        last_mov = BancoMovimento.objects.filter(descricao=mov.descricao).exclude(justificacao='').order_by('data').last()
        if last_mov and last_mov.justificacao_doc:
            return last_mov.justificacao_doc.terceiro.nome
        return ''

    def overview(self, return_data=False):
        d = totals(self.current_condo, self.current_year)
        
        print("\nR E S U M O\n")
        print(f"Data: {d['today']}  dia: {d['day_of_year']} ({d['percent_year']:>6.2f}%)\n")

        print(f"Orçamento:       {d['valor_orcamento']:>10.2f}")
        print(f"Despesas pagas:  {d['despesas_pagas']:>10.2f}  ({d['percent_orcamento']:5.2f}%)")
        print(f'Folga:           {d['orcamento_folga']:>10.2f}')
        print(f"Despesas s/orc.: {d['despesas_pagas_sem_orcamento']:>10.2f}")
        print(f'FCR:             {d['fcr']:>10.2f}\n')

        print(f"Valor <2024:            {d['valor_previsto_pre2024']:>10.2f}")
        print(f"Valor recebido <2024:   {d['valor_recebido_pre2024']:>10.2f}")
        print(f"Valor por receber <2024 {d['valor_a_receber_pre2024']:>10.2f}\n")

        print(f"Valor previsto:  {d['valor_previsto']:>10.2f}")
        print(f"Valor recebido:  {d['valor_recebido']:>10.2f}")
        print(f"Valor a receber: {d['valor_a_receber']:>10.2f}")
        print(f"Valor em atraso: {d['valor_em_atraso']:>10.2f}")


        # crosstab despesas
        print("\n\nD E S P E S A S\n")
        ct_despesas = crosstab_despesas(self.current_condo, self.current_year)
        print(f"{'rubrica':20} {'Orcamento'} {'Total':>7} ", end="")
        print(''.join([f'{mes[:3]:>7} ' for mes in MONTHS]))
        for k, lst in ct_despesas.items():
            print(f"{k.rubrica:20} {k.valor:>9.2f} ", end="")
            for i in range(13):
                print(f"{lst[i]:>7.2f} ", end="")
            print("")
        
        # crosstab receitas
        print("\n\nR E C E I T A S\n")
        ct_receitas = crosstab_receitas(self.current_condo, self.current_year)
        print(f'{"Fracao":17} <<<<<<< ', end="")
        print(''.join([f'{mes[:3]:>7} ' for mes in MONTHS]))
        for k, lst in ct_receitas.items():
            print(f"{k.nome_abrev} {k.nome:15} ", end="")
            for i in range(13):
                print(f"{lst[i]:>7.2f} ", end="")
            print(k.proprietario)

        if return_data:
            return locals()


    def justificar_movimentos(self):
        options_completer = WordCompleter(['despesa', 'receita'])
        for mov in BancoMovimento.objects.filter(justificacao='').order_by('data'):
            print('-'*80)
            print(mov)
            print('-'*80)
            option = prompt('> ',completer=options_completer, default='receita' if mov.valor>0 else 'despesa')
            match option:
                case '':
                    continue
                case 'despesa':
                    doc = self.registar_despesa(mov)
                    mov.justificacao_doc = doc
                    mov.justificacao = 'DESPESA ' + str(doc)
                case 'receita':
                    doc = self.registar_receita_recibo(mov)
                    mov.justificacao_doc = doc
                    mov.justificacao = 'RECIBO ' + str(doc)
                case '0':
                    break
                case _:
                    mov.justificacao = option
            mov.save()


    def registar_despesa(self, mov:BancoMovimento=None):
        data = prompt(
            'data > ',
            default=str(mov.data if mov else date.today()),
            validator=self.date_validator
        )       
        valor = prompt(
            'valor > ',
            default=str(-mov.valor if mov else None),
            validator=self.decimal_validator
        )
        numero = prompt('numero > ')
        terceiro_nome = prompt(
            'fornecedor > ', 
            #default=self.hints.get(mov.descricao,''),
            default= self.get_terceiro_from_bancomov(mov),
            completer=self.fornecedor_completer,
            validator=self.fornecedor_validator
        )
        # self.hints[mov.descricao] = terceiro_nome
        
        terceiro = Terceiro.objects.get(nome=terceiro_nome)
        last_despesa_from_terceiro = Despesa.objects.filter(terceiro=terceiro).last()
        default_rubrica = last_despesa_from_terceiro.rubrica.rubrica if last_despesa_from_terceiro else ''
        rubrica_nome = prompt(
            'rubrica > ', 
            completer=self.rubrica_completer, 
            default=default_rubrica,
            validator=self.rubrica_validator
        )

        despesa = Despesa(
            terceiro=terceiro,
            data=datetime.strptime(data, '%Y-%m-%d').date(),
            numero=numero,
            valor=Decimal(valor),
            rubrica=self.orcamento.rubricas.get(rubrica=rubrica_nome)
        )

        despesa.save()
        return despesa


    def registar_receita_recibo(self, mov:BancoMovimento=None):
        data = prompt(
            'data > ',
            default=str(mov.data if mov else date.today()),
            validator=self.date_validator
        )
        data = datetime.strptime(data, '%Y-%m-%d').date()
        
        valor = prompt(
            'valor > ',
            default=str(mov.valor if mov else None),
            validator=self.decimal_validator
        )
        valor = Decimal(valor)

        terceiro_nome = prompt(
            'Proprietario > ',
            #default=self.hints.get(mov.descricao,''),
            default= self.get_terceiro_from_bancomov(mov),
            completer=self.proprietario_completer,
            validator=self.proprietario_validator
        )       
        terceiro = Terceiro.objects.get(nome=terceiro_nome)
        #self.hints[mov.descricao] = terceiro_nome
        has_2023 = Receita.objects.filter(fracao__proprietario=terceiro, data__year=2023, valor_por_pagar__gt=0).exists()

        # criar/editar recibo
        qs = Receita.objects.filter(fracao__proprietario=terceiro, valor_por_pagar__gt=0).order_by('data')
        qs = list(qs)

        def apply(condition):
            nonlocal valor
            for receita in qs:
                if valor <= 0.001:
                    break
                if condition(receita):
                    if valor < receita.valor_por_pagar:
                        receita.tmp_valor_a_pagar = valor
                        valor = 0
                    else:
                        receita.tmp_valor_a_pagar = receita.valor_por_pagar
                        valor -= receita.valor_por_pagar

        # due Q,F,X types then 'O' then future
        if has_2023 and prompt("liquidar primeiro valores 2023? (s/n): ", default='n') == 's':
            apply(lambda r: r.tipo in ['O'] and r.data <= data)
        apply(lambda r: r.tipo in ['Q', 'F', 'X', 'M', 'E'] and r.data <= data)
        apply(lambda r: r.tipo in ['O'] and r.data <= data)
        apply(lambda r: r.data > data)

        # confirm
        total = Decimal('0.00')
        for receita in qs:
            if hasattr(receita, 'tmp_valor_a_pagar'):
                total += receita.tmp_valor_a_pagar
                print(f'{receita} {receita.tmp_valor_a_pagar:>10.2f}')
        print(f"TOTAL {total:>10.2f}€")
        if prompt("Confirma emissão de recibo? (s/n):", default='s') != 's':
            return

        # gravar recibo
        with transaction.atomic():
            ano, numero = DocNumber.objects.get_number_for_doc(self.current_condo, 'CONDO_RECIBO', data.year)
            recibo = Recibo.objects.create(
                condo=self.current_condo,
                terceiro=terceiro,
                ano=ano,
                numero=numero,
                data=date.today(),
                data_pagamento=data,
                descricao='',
                valor=total
            )
            for receita in qs:
                if hasattr(receita, 'tmp_valor_a_pagar') and receita.tmp_valor_a_pagar >0:
                    ReciboReceita.objects.create(
                        recibo=recibo,
                        receita=receita,
                        valor=receita.tmp_valor_a_pagar
                    )

        return recibo


    def enviar_avisos(self):
        # EVITAR reenvio ????
        if prompt('Confirma o envio de avisos? (s/n)') != 's':
            return
        enviar_avisos(self.current_condo)

        # today = datetime.today()
        # qs = (Receita.objects
        #     .filter(
        #         fracao__condo=self.current_condo, 
        #         valor_por_pagar__gt=0,
        #         data__lte=today
        #         # fracao__proprietario__nif__in=['216527350']
        #     )
        #     .select_related('fracao__proprietario')
        #     .order_by('fracao__nome_abrev', 'data')
        # )
        
        # # agrupar por terceiro
        # grouped_data = defaultdict(list)
        # for receita in qs:
        #     grouped_data[receita.fracao.proprietario].append(receita)
        
        # for terceiro, lista in grouped_data.items():
        #     # para cada proprietário compor html enviar email
        #     total = sum(r.valor_por_pagar for r in lista)
        #     context = {
        #         'condo': self.current_condo,
        #         'terceiro': terceiro,
        #         'lista': lista,
        #         'total': total
        #     }
        #     print(f'Aviso {str(terceiro):60} {total:10.2f} ({len(lista)})')
        #     html_msg = render_to_string('condo/email_aviso_debitos.html', context)
        #     msg = EmailHtml(f'{self.current_condo.nome} - Aviso de Débito {MONTHS[today.month-1]}', to=[terceiro.email])
        #     msg.attach_html(html_msg)
        #     msg.send()


    def enviar_recibos(self):
        qs = Recibo.objects.filter(condo=self.current_condo, enviado=False)
        if not qs.exists():
            print('não existem recibos por enviar!')
            return
        if prompt('Confirma o envio de recibos? (s/n)') != 's':
            return
        qs = qs.select_related('terceiro')\
               .prefetch_related(
                   Prefetch('reciboreceita_set', queryset=ReciboReceita.objects.select_related('receita__fracao')))\
               .order_by('numero')
        for recibo in qs:
            context ={
                'condo': self.current_condo,
                'recibo': recibo,
                'terceiro': recibo.terceiro,
                'lista': recibo.reciboreceita_set.all(),
            }
            print(f'Recibo {recibo}')
            with transaction.atomic():
                html_msg = render_to_string('condo/email_recibo.html', context)
                msg = EmailHtml(f'{self.current_condo.nome} - Recibo {recibo.ano}/{recibo.numero}', to=[recibo.terceiro.email])
                msg.attach_html(html_msg)
                msg.send()        
                recibo.enviado = True
                recibo.save()

    def enviar_overview(self):
        if prompt('Confirma o envio do OVERVIEW? (s/n)') != 's':
            return
        try:
            sys.stdout = io.StringIO()
            overview = self.overview(True)
        finally:
            sys.stdout = sys.__stdout__
        context ={
            'current_month': date.today().month,
            'overview': overview,
            'overview_despesas': overview['ct_despesas'],
            'overview_receitas': overview['ct_receitas'],
            'movimentos_bancarios': BancoMovimento.objects.filter(empresa=self.current_condo, data__year=self.current_year)
        }
        bcc = [t.email for t in Terceiro.objects.filter(fracao__condo=self.current_condo)]
        html_msg = render_to_string('condo/email_overview.html', context)
        msg = EmailHtml(f'{self.current_condo.nome} - Resumo', bcc=bcc)
        msg.attach_html(html_msg)
        for fn in glob.glob('/home/<USER>/Downloads/Montepio_*.pdf'):
            msg.attach_file(fn)
        msg.send()        


    def despesa_fillin_doc(self):
        """
        dataref - created to window the query and not grow indefinitely
        get docs from email not assigned to despesas yet
        get despesas with no doc yet
        in order of date try from the available docs match the despesa
        """
        dataref = timezone.now() - timedelta(days=300)

        docs = list(Doc.objects.filter(emails__received_at__gte=dataref, despesa__isnull=True, qrcode_data__isnull=False))
        for doc in docs:
                doc.qrinfo = ETCodeQR.from_qr(doc.qrcode_data)
        
        for despesa in Despesa.objects.filter(data__gte=dataref, doc__isnull=True).select_related('terceiro').order_by('data'):
            for d in docs:
                if (d.qrinfo.nif_do_emitente == despesa.terceiro.nif and 
                   d.qrinfo.data_do_documento <= despesa.data and 
                   d.qrinfo.total_do_documento == despesa.valor
                ):
                    print('ASSOCIATION ', despesa, d)
                    despesa.doc = d
                    despesa.save()
                    docs.remove(d)
                    break