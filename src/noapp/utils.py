import uuid

from django.http import HttpResponse
from django.shortcuts import render, get_object_or_404
from django.views.generic import View
from django.forms import Form, ModelForm, modelform_factory


class MultiStepFormView(View):
    model = None
    forms = []
    template_name = "noapp/multi_step_form.html"
    browser_autocomplete = True

    def get_wizard_id(self, request):
        # Retrieve or generate a unique wizard ID.
        if "wizard_id" in request.POST:
            return request.POST["wizard_id"]
        # Generate a new ID if none exists
        new_id = str(uuid.uuid4())
        request.session[f"{new_id}_current_step"] = 0
        return new_id

    def get_current_step(self, request, wizard_id):
        return int(request.session.get(f"{wizard_id}_current_step", 0))

    def get_current_data(self, request, wizard_id):
        current_data = {}
        for idx in range(len(self.forms)):
            current_data.update(request.session.get(f"{wizard_id}_step{idx}_data", {}))
        return current_data

    def get_form_class(self, step):
        """Retrieve the form class for a step."""
        step_form = self.forms[step]
        if isinstance(step_form, type) and issubclass(step_form, Form):
            return step_form
        elif isinstance(step_form, type) and issubclass(step_form, ModelForm):
            return step_form
        elif isinstance(step_form, (list, tuple)):
            # Dynamically create a form for the given fields
            return modelform_factory(self.model, fields=step_form)
        else:
            raise ValueError("Steps must be a ModelForm or a list of fields.")

    def get_form(self, step, wizard_id, data=None, instance=None):
        FormClass = self.get_form_class(step)
        initial = {}
        if hasattr(FormClass, 'get_initial'):
            previous_data = self.get_current_data(self.request, wizard_id)
            initial = FormClass.get_initial(self.request, previous_data)
        kwargs = {}
        if data:
            kwargs['data'] = data
        if initial:
            kwargs['initial'] = initial
        if instance:
            kwargs['instance'] = instance
        return FormClass(**kwargs)

    def get_object(self, request, pk):
        """Retrieve the model instance being edited."""
        return get_object_or_404(self.model, pk=pk)

    def get(self, request, pk=None, *args, **kwargs):
        wizard_id = self.get_wizard_id(request)
        step = self.get_current_step(request, wizard_id)
        instance = self.get_object(request, pk) if self.model else None
        form = self.get_form(step, wizard_id, request.session.get(f"{wizard_id}_step{step}_data", None), instance)

        # when entering a step where already been back next
        # assigning incomplete data will trigger errors, but this is a get just displaying the form
        # so clear errors in this situation
        form._errors = {} # hack
        context = {
            "form": form, 
            "step": step, 
            "wizard_id": wizard_id, 
            'autocomplete': 'on' if self.browser_autocomplete else 'off',
        }
        return render(request, self.template_name, context)

    def post(self, request, pk=None, *args, **kwargs):
        wizard_id = self.get_wizard_id(request)
        step = self.get_current_step(request, wizard_id)
        instance = self.get_object(request, pk) if self.model else None

        form = self.get_form(step, wizard_id, request.POST, instance)
        valid = form.is_valid()
        request.session[f"{wizard_id}_step{step}_data"] = form.cleaned_data

        # Handle "Back" button
        if "back" in request.POST:
            if step > 0:
                request.session[f"{wizard_id}_current_step"] = step - 1
            return self.get(request, pk)

        # Handle "Next" or "Submit"
        if valid:
            if step + 1 < len(self.forms):
                request.session[f"{wizard_id}_current_step"] = step + 1
                return self.get(request, pk)

            # Final step: Process and save all data
            # Clear session data for the form
            full_data = self.get_current_data(request, wizard_id)
            for key in list(request.session.keys()):
                if key.startswith(wizard_id):
                    del request.session[key]
            return self.final_step(full_data)

        # If form is invalid, re-render current step
        context = {
            "form": form, 
            "step": step, 
            "wizard_id": wizard_id, 
            'autocomplete': 'on' if self.browser_autocomplete else 'off',
        }        
        return render(request, self.template_name, context)

    def final_step(self, data):
        return HttpResponse(str(data))

