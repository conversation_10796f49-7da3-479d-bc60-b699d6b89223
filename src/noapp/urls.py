from django.urls import path
from django import forms

from .utils import MultiStepFormView
from terceiro.models import Terceiro

# Forms defined declaratively
class Step1Form(forms.Form):
    first_name = forms.CharField(max_length=50)
    last_name = forms.CharField(max_length=50)
    email = forms.EmailField()

class Step2Form(forms.Form):
    address = forms.CharField(max_length=255)
    city = forms.CharField(max_length=50)
    postal_code = forms.CharField(max_length=10)

    @staticmethod
    def get_initial(request, current_data):
        initial = {}
        if current_data['email'] == '<EMAIL>':
            initial['postal_code'] = '4580-100'
        return initial

class Step3Form(forms.Form):
    confirmation = forms.BooleanField(label="Confirm the above information")

class Wizardform1(MultiStepFormView):
    forms = [Step1Form, Step2Form, Step3Form]
    browser_autocomplete = True



class Wizardform2(MultiStepFormView):
    model = Terceiro
    forms = [
        ['nome', 'morada', 'cpostal', 'cpostal_alt', 'naturalidade'], 
        ['nif', 'niss', 'ncga', 'iban', 'bic'], 
        ('di_tipo', 'di_numero', 'di_data_validade', 'di_arquivo', 'data_nascimento')
    ]
    browser_autocomplete = True



app_name = 'noapp'
urlpatterns = [
    path('wz1', Wizardform1.as_view(), name='wz1'),
    path('wz2/<int:pk>', Wizardform2.as_view(), name='wz2'),
]