<div id="form-container">
    <form
        hx-post="{{request.path}}"
        hx-target="#form-container"
        hx-swap="innerHTML"
        novalidate 
        autocomplete="{{autocomplete}}">
        {% csrf_token %}
        <input type="hidden" name="wizard_id" value="{{ wizard_id }}">
        {{ form.as_p }}
        <div>
            {% if step > 0 %}
                <button type="submit" name="back">Back</button>
            {% endif %}
            <button type="submit">
                {% if step < 2 %} Next {% else %} Submit {% endif %}
            </button>
        </div>
    </form>
</div>
