from core.importer import SQLImporter


class Feriados(SQLImporter):
    filename = '../luxoconf/feriado.csv'
    fields = 'data,nome'
    skip_first_line = True


class Linhas(SQLImporter):
    filename = '../luxoconf/linha.csv'
    fields = 'id,nome,n_pessoas,dias_em_curso,coeficiente,mins_dia,activa,externa'
    skip_first_line = True


class Encomendas(SQLImporter):
    filename = '../luxoconf/encomenda.csv'    
    fields = 'id,ano,numero,ref,data,data_limite,estacao_id,estacao_ref,cliente_ref,modelo_ref,modelo_nome,modelo_tempo_mins,modelo_preco_un,qtd,faltas,ok_data,ok_producao,data_amostra,data_ft,data_pc,data_molde,data_okprod,estadoid,obs,_data_prev'
    skip_first_line = True


class Jobs(SQLImporter):
    filename = '../luxoconf/job.csv'
    fields = 'id,encomenda_id,linha_id,linha_n_pessoas,linha_coeficiente,linha_mins_dia,linha_dias_em_curso,ordem,qtd,delta,corte_in,corte_out,linha_in,linha_out,armaz_in,armaz_out,data_ini,data_fim,_dias_prev,_dias_prev_acum,_data_prev,_qtd_dia'
    skip_first_line = True


class JobMovs(SQLImporter):
    filename = '../luxoconf/job_mov.csv'
    fields = 'id,job_id,data,corte_in,corte_ue,corte_out,corte_us,linha_in,linha_ue,linha_out,linha_us,armaz_in,armaz_ue,armaz_out,armaz_us'
    skip_first_line = True

    def after_import(self):
        self.runsql('''
            insert into gpt_feriado 
            select data::date, nome from tmp_feriado;
            
            insert into gpt_linha(id, nome, n_pessoas, dias_em_curso, coeficiente, mins_dia, activa, externa)
            select id::bigint,nome,n_pessoas::int,dias_em_curso::int,coeficiente::numeric,mins_dia::int,activa::bool,coalesce(externa,'False')::bool
            from tmp_linha;

            insert into gpt_linha(id, nome, n_pessoas, dias_em_curso, coeficiente, mins_dia, activa, externa)
            select distinct linha_id::int, 'Undefined',1, 1, 1, 1, 0::bool, 0::bool 
            from tmp_job where linha_id::int not in (select id from gpt_linha);

            insert into gpt_encomenda
            select id::int,ano::int,numero::int,ref,data::date,data_limite::date,estacao_id::int,estacao_ref,
                cliente_ref,modelo_ref,modelo_nome,modelo_tempo_mins::int,modelo_preco_un::numeric,qtd::int,
                faltas,ok_data::date,ok_producao::bool,data_amostra::date,data_ft::date,data_pc::date,data_molde::date,data_okprod::date,
                estadoid::int,obs,_data_prev::date
            from tmp_encomenda;

            insert into gpt_job (id,encomenda_id,linha_id,
                linha_n_pessoas,linha_coeficiente,linha_mins_dia,linha_dias_em_curso,
                ordem,qtd,delta,
                corte_in,corte_out,
                linha_in,linha_out,
                armaz_in,armaz_out,
                data_ini,data_fim,
                _dias_prev,_dias_prev_acum,_data_prev,_qtd_dia)
            select id::int,encomenda_id::int,linha_id::int,
                linha_n_pessoas::int,linha_coeficiente::numeric,linha_mins_dia::int,linha_dias_em_curso::int,
                ordem::int,qtd::int,delta::int,
                corte_in::int,corte_out::int,
                linha_in::int,linha_out::int,
                armaz_in::int,armaz_out::int,
                data_ini::date,data_fim::date,
                _dias_prev::numeric,_dias_prev_acum::numeric,_data_prev::date,_qtd_dia::numeric
            from tmp_job;

            delete from tmp_job_mov where job_id::int not in (select id from gpt_job);
            
            insert into gpt_jobmov(id,job_id,data,
                corte_in,corte_ue,corte_out,corte_us,
                linha_in,linha_ue,linha_out,linha_us,
                armaz_in,armaz_ue,armaz_out,armaz_us)
            select id::int, job_id::int, data::date,
                corte_in::int, corte_ue::bool, corte_out::int, corte_us::bool,
                linha_in::int, linha_ue::bool, linha_out::int, linha_us::bool,
                armaz_in::int, armaz_ue::bool, armaz_out::int, armaz_us::bool
            from tmp_job_mov;
        ''')

