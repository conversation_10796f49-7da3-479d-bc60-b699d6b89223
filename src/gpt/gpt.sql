--  T A B L E S


-- DROP TABLE common_feriado;
-- DROP TABLE gpt_data_prev;
-- DROP TABLE gpt_linha  CASCADE;
-- DROP TABLE gpt_encomenda CASCADE;
-- DROP TABLE gpt_job CASCADE;
-- DROP TABLE gpt_jobmov;
-- DROP VIEW gpt_v_encomenda;
-- DROP VIEW gpt_v_encomenda_detail;

-- DROP PROCEDURE gpt_fill_data_prev;
-- DROP PROCEDURE gpt_calc;
-- DROP PROCEDURE gpt_encomenda_post;
-- DROP PROCEDURE gpt_encomenda_2_linha;
-- DROP PROCEDURE gpt_encomenda_close;
-- DROP PROCEDURE gpt_encomenda_open;
-- DROP PROCEDURE gpt_encomenda_del;
-- DROP PROCEDURE gpt_jobmove;
-- DROP PROCEDURE gpt_job_split;
-- DROP PROCEDURE gpt_job_merge;
-- DROP PROCEDURE gpt_jobmov_post;
-- DROP PROCEDURE gpt_jm_j_sync;
-- DROP PROCEDURE gpt_j_jm_sync;


-- CREATE TABLE IF NOT EXISTS common_feriado(
--     data date PRIMARY KEY,
--     nome varchar(50) NOT NULL
-- );


-- CREATE TABLE IF NOT EXISTS gpt_data_prev(
--     n int,
--     data date
-- );


-- CREATE TABLE IF NOT EXISTS gpt_linha(
--     id int PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
--     nome varchar(30) NULL,
--     n_pessoas int NULL,
--     dias_em_curso int NULL,
--     coeficiente decimal(5, 2) NULL,
--     mins_dia int NULL,
--     activa boolean NULL,
--     externa boolean NULL
-- );


-- CREATE TABLE IF NOT EXISTS gpt_encomenda(
--     id int PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
--     ano int NULL,
--     numero int NULL,
--     ref varchar(20) NULL,
--     data timestamp NULL,
--     data_limite timestamp NULL,
--     estacao_id int NULL,
--     estacao_ref char(10) NULL,
--     cliente_ref char(20) NULL,
--     modelo_ref char(20) NULL,
--     modelo_nome char(50) NULL,
--     modelo_tempo_mins int NULL,
--     modelo_preco_un money NULL,
--     qtd int NULL,
--     faltas varchar(50) NULL,
--     ok_data timestamp NULL,
--     ok_producao boolean NULL,
--     data_amostra timestamp NULL,
--     data_ft timestamp NULL,
--     data_pc timestamp NULL,
--     data_molde timestamp NULL,
--     data_okprod timestamp NULL,
--     estadoid int NULL,
--     obs text NULL,
--     _data_prev timestamp NULL
-- );
-- create index if not exists gpt_encomenda_estadoid_ix on gpt_encomenda(estadoid) where estadoid=1;


-- CREATE TABLE IF NOT EXISTS gpt_job(
--     id int PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
--     encomenda_id int NOT NULL REFERENCES gpt_encomenda,
--     linha_id int NOT NULL REFERENCES gpt_linha,
--     linha_n_pessoas int NULL,
--     linha_coeficiente float NULL,
--     linha_mins_dia int NULL,
--     linha_dias_em_curso int NULL,
--     ordem int NULL,
--     qtd int NULL,
--     delta int NULL,
--     corte_in int NULL,
--     corte_out int NULL,
--     linha_in int NULL,
--     linha_out int NULL,
--     armaz_in int NULL,
--     armaz_out int NULL,
--     data_ini date NULL,
--     data_fim date NULL,
--     _dias_prev float NULL,
--     _dias_prev_acum float NULL,
--     _data_prev timestamp NULL,
--     _qtd_dia float NULL
-- );
-- create index if not exists gpt_job_encomenda_id_fk on gpt_job(encomenda_id);
-- create index if not exists gpt_job_linha_id_fk on gpt_job(linha_id);


-- CREATE TABLE IF NOT EXISTS gpt_jobmov(
--     id int PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
--     job_id int NOT NULL REFERENCES gpt_job,
--     data date NOT NULL,
--     corte_in int NULL,
--     corte_ue boolean NULL,
--     corte_out int NULL,
--     corte_us boolean NULL,
--     linha_in int NULL,
--     linha_ue boolean NULL,
--     linha_out int NULL,
--     linha_us boolean NULL,
--     armaz_in int NULL,
--     armaz_ue boolean NULL,
--     armaz_out int NULL,
--     armaz_us boolean NULL
-- );
-- create index if not exists gpt_jobmov_job_fk on gpt_jobmov(job_id);



-- V I E W S 

DROP VIEW IF EXISTS gpt_v_encomenda CASCADE;
CREATE view gpt_v_encomenda
as
select e.*,
    jobs.qtd_job,
    jobs.qtd_linha_out,
    jobs.qtd_armaz_out,
    round(e.qtd * e.modelo_preco_un::decimal, 2) valor
from gpt_encomenda e
left join lateral (
    select encomenda_id, SUM(qtd) qtd_job, SUM(linha_out) qtd_linha_out, SUM(armaz_out) qtd_armaz_out
    from gpt_job
    where encomenda_id=e.id
    group by encomenda_id
) jobs on true;


DROP VIEW IF EXISTS gpt_v_encomenda_detail CASCADE;
CREATE view gpt_v_encomenda_detail
as
select e.*,
  j.linha_id,
  l.nome linha, 
  j.qtd j_qtd, 
  j.data_ini j_data_ini,
  j.data_fim j_data_fim,
  jm.data jm_data, 
  jm.corte_in, jm.corte_ue,
  jm.corte_out, jm.corte_us,
  jm.linha_in, jm.linha_ue,
  jm.linha_out, jm.linha_us,
  jm.armaz_in, jm.armaz_ue,
  jm.armaz_out, jm.armaz_us
from gpt_v_encomenda e
left join gpt_job j on e.id=j.encomenda_id
left join gpt_linha l on j.linha_id=l.id
left join gpt_jobmov jm on j.id=jm.job_id;



-- P R O C E D U R E S

--  t timestamp;
--  t = clock_timestamp();
--  raise notice 'time spent %', clock_timestamp() -t;



/*CREATE OR REPLACE PROCEDURE gpt_fill_data_prev() AS $$
--fill data_prev table
--from now
--estimate n days after according to working days and table feriados/ferias
declare 
    running_date date;
    i int =0;
    t timestamptz = clock_timestamp();
begin
    running_date = current_date;

    truncate table gpt_data_prev;

    LOOP
        EXIT when i>=1000;
        running_date = running_date + 1;
        
        CONTINUE when date_part('dow', running_date) in (0,6);
        CONTINUE when EXISTS(select * from common_feriado where data=running_date);
        i = i + 1;
        insert into gpt_data_prev(n, data) values(i, running_date);
    END LOOP;   
    raise notice 'time spent=%', clock_timestamp() - t;
end
$$ LANGUAGE plpgsql;
*/


CREATE OR REPLACE PROCEDURE gpt_fill_data_prev() AS $$
--fill data_prev table
--from now
--estimate n days after according to working days and table feriados/ferias
begin
    truncate table gpt_data_prev;
    with datas as (
        select tmp.data
        from generate_series((current_date+1)::date, (current_date+1000)::date, interval '1 day') as tmp(data)
        left join common_feriado on tmp.data=common_feriado.data
        where date_part('dow', tmp.data) not in (0, 6) and common_feriado.data is null
    )
    insert into gpt_data_prev(n, data)
    select row_number() over(), data from datas;
end
$$ LANGUAGE plpgsql;


CREATE OR REPLACE PROCEDURE gpt_calc() AS $$
begin
    -- reorder jobs because adds,splits or merges migth have generated gaps
    -- fill ordem, _qtd_dia, _dias_prev
    with activejobs as (
        select j.id, 
            row_number() over (partition by linha_id order by ordem) as new_ordem,
            case 
                when j.data_fim is not null then 0 
                when coalesce(j.linha_n_pessoas * j.linha_coeficiente * j.linha_mins_dia,0)=0 then 0
                else ((j.qtd + j.delta - j.linha_out) * e.modelo_tempo_mins * 1.0) / (j.linha_n_pessoas * j.linha_coeficiente * j.linha_mins_dia)
            end dias_prev,
            case
                when coalesce(e.modelo_tempo_mins,0)=0 then 0
                else (j.linha_n_pessoas * j.linha_coeficiente * j.linha_mins_dia) / e.modelo_tempo_mins
            end qtd_dia
        from gpt_job j
        left join gpt_encomenda e on j.encomenda_id=e.id
        where ordem<>0
    )
    update gpt_job 
    set ordem = new_ordem, _qtd_dia = qtd_dia, _dias_prev = dias_prev
    from activejobs 
    where gpt_job.id=activejobs.id;

    -- fill _dias_prev_acum
    with activejobs as (
        select id, SUM(_dias_prev) over(partition by linha_id order by ordem) dias_prev_acum
        from gpt_job
        where ordem <> 0
    )
    update gpt_job 
    set _dias_prev_acum = dias_prev_acum
    from activejobs 
    where gpt_job.id=activejobs.id;

    -- fill _data_prev
    call gpt_fill_data_prev();

    with activejobs as (
        select id, coalesce(j.data_fim, dp.data) data_prev
        from gpt_job j
        left join gpt_data_prev dp on (trunc(j._dias_prev_acum)+j.linha_dias_em_curso)=dp.n
        where ordem <> 0
    )
    update gpt_job 
    set _data_prev = data_prev
    from activejobs 
    where gpt_job.id=activejobs.id;

    -- fill encomenda data_prev
    with activejobs as (
        select encomenda_id, SUM(qtd) s_qtd, MAX(_data_prev)  max_data_prev
        from gpt_job
        where ordem<>0
        group by encomenda_id       
    )
    update gpt_encomenda
    set _data_prev = case when qtd=s_qtd then max_data_prev else null end
    from activejobs
    where gpt_encomenda.id=activejobs.encomenda_id;

end
$$ LANGUAGE plpgsql;



CREATE OR REPLACE PROCEDURE gpt_encomenda_post(_id int)
as $$
declare 
    r RECORD;
    _new_numero int;
begin
    select ano, numero, data into r
    from gpt_encomenda
    where id=_id;

    if r.ano is null then
        select coalesce(MAX(numero),0)+1 
        into _new_numero
        from gpt_encomenda 
        where date_part('year', data)=date_part('year', r.data);
        
        update gpt_encomenda
        set ano=date_part('year', data), numero=_new_numero
        where id=_id;
    end if;
end
$$ LANGUAGE plpgsql;



CREATE OR REPLACE PROCEDURE gpt_encomenda_close(_id int)
as $$
begin
    update gpt_encomenda set estadoid=2 where id=_id;
    update gpt_job set ordem=0 where encomenda_id=_id;

    call gpt_calc();
end
$$ LANGUAGE plpgsql;



CREATE OR REPLACE PROCEDURE gpt_encomenda_open(_id int)
as $$
begin
    update encomenda set estadoid=1 where id=_id;
    update job set ordem=9999 where encomenda_id=_id;

    call gpt_calc();
end
$$ LANGUAGE plpgsql;



CREATE OR REPLACE PROCEDURE gpt_encomenda_del(_id int)
as $$
begin
    delete from gpt_jobmov where job_id in (select id from gpt_job where encomenda_id=_id);
    delete from gpt_job where encomenda_id=_id;
    delete from gpt_encomenda where id=_id;

    call gpt_calc();
end
$$ LANGUAGE plpgsql;



CREATE OR REPLACE PROCEDURE gpt_encomenda_2_linha(_encomenda_id int, _linha_id int, _qtd int) as $$
declare 
    qtd_encomenda int;
    qtd_job int;
begin
    if _qtd<=0 then return; end if;

    select qtd into qtd_encomenda from gpt_encomenda where id=_encomenda_id;
    select SUM(qtd) into qtd_job from gpt_job where encomenda_id=_encomenda_id;

    if qtd_job + _qtd > _qtd_encomenda then 
        _qtd = qtd_encomenda - qtd_job;
    end if;
    if _qtd<=0 then return; end if;

    insert into gpt_job(
        encomenda_id,
        linha_id,
        linha_n_pessoas, linha_coeficiente, linha_mins_dia, linha_dias_em_curso, 
        ordem,
        qtd, delta, 
        corte_in, corte_out,
        linha_in, linha_out,
        armaz_in, armaz_out)
    select 
        _encomenda_id,
        _linha_id,
        n_pessoas, coeficiente, mins_dia, dias_em_curso,
        9999,  
        _qtd, 0,
        0, 0, 
        0, 0,
        0, 0
    from gpt_linha
    where id=_linha_iD;

    call gpt_calc();
end
$$ LANGUAGE plpgsql;



CREATE OR REPLACE PROCEDURE gpt_jobmove(job_id int, dir int) AS $$
declare
    j_linha_id int;
    j_ordem int;
    max_ordem int;
begin
    select linha_id, ordem into j_linha_id, j_ordem
    from gpt_job
    where id=job_id;

    select max(ordem) into max_ordem
    from gpt_job
    where linha_id=j_linha_id and ordem<>0;

    if (dir=-1) and (j_ordem=1) then return; end if;
    if (dir=1) and (j_ordem=max_ord) then return; end if;

    update job set ordem=j_ordem        where linha_id=j_linha_id and ordem=j_ordem + dir;
    update job set ordem=j_ordem + dir  where id=job_id;

    call gpt_calc();
end
$$ LANGUAGE plpgsql;



CREATE OR REPLACE PROCEDURE gpt_job_split(j_id int, t_linha_id int, t_qtd int) AS $$
--split the job in 2
--to destination line, 
--may be the same line just split qtd
declare
    j_encomenda_id int;
    j_linha_id int;
    j_qtd int;
    j_linha_in int;
    new_job_id int;
begin
    select encomenda_id, linha_id, qtd, linha_in
    into j_encomenda_id, j_linha_id, j_qtd, j_linha_in
    from gpt_job
    where id=j_id;

    if t_qtd > (j_qtd - j_linha_in) then
        raise exception 'Quantidade a mover superior à possível!';
    end if;

    if t_qtd = j_qtd then
        -- move all job
        update gpt_job
        set linha_id = t_linha_id, ordem = 9999
        where id = j_id;
    else
        update gpt_job 
        set qtd = qtd - t_qtd
        where id=j_id;

        insert into gpt_job(
            linha_id, encomenda_id, ordem,
            qtd, delta, 
            corte_in, corte_out, linha_in, linha_out, armaz_in, armaz_out)
        values(
            t_linha_id, j_encomenda_id, 9999,
            t_qtd, 0,
            0, 0, 0, 0, 0, 0)
        returning id into new_job_id;

        --criar movimentos de correcao no corte se necessario
        call sp_j_jm_sync(j_id, null, new_job_id); 
    end if;

    call gpt_calc();
end
$$ LANGUAGE plpgsql;



CREATE OR REPLACE PROCEDURE gpt_job_merge(j_id int) AS $$
-- all jobs for the same encomenda_id will be merged
-- only qtd not in line will be merged to respect stats
declare
    j_encomenda_id int;
    tmp record;
begin
    select encomenda_id into j_encomenda_id
    from gpt_job
    where id=j_id;

    if not exists(select id from gpt_job where encomenda_id=j_encomenda_id and id<>j_id) then
        return;
    end if;

    FOR tmp IN
        --valores que nos jobs se podem retirar
        select id, qtd, qtd -linha_in qtd_2_move
        from gpt_job
        where encomenda_id=j_encomenda_id and id<>j_id and qtd>linha_in
    LOOP
        -- retirar de outros jobs
        if tmp.qtd = tmp.qtd_2_move then
            delete from gpt_job where id = tmp.id;
        else
            update gpt_job 
            set qtd = qtd - qtd_2_move
            where id = tmp.id;
        end if;
        -- colocar no job
        update gpt_job
        set qtd = qtd + qtd_2_move
        where id = j_id;
    END LOOP;

    --sync jov_movs due to corte vs qtd  issues
    call sp_j_jm_sync(null, j_encomenda_id, j_id);

    call gpt_calc();
end
$$ LANGUAGE plpgsql;



CREATE OR REPLACE PROCEDURE gpt_jm_j_sync(j_id int, e_id int) AS $$
begin
    with tmp as (
        select job_id,
            SUM(corte_in)   s_corte_in,
            SUM(corte_out)  s_corte_out,
            SUM(linha_in)   s_linha_in,
            SUM(linha_out)  s_linha_out,
            SUM(armaz_in)   s_armaz_in,
            SUM(armaz_out)  s_armaz_out,
            MIN(case when corte_in is not null then data else null end) s_data_ini, 
            MAX(case when armaz_ue=1 then data else null end) s_data_fim,
            SUM(case when corte_ue=1 then 1 else 0 end) s_corte_ue, 
            SUM(case when corte_us=1 then 1 else 0 end) s_corte_us, 
            SUM(case when linha_ue=1 then 1 else 0 end) s_linha_ue, 
            SUM(case when linha_us=1 then 1 else 0 end) s_linha_us, 
            SUM(case when armaz_ue=1 then 1 else 0 end) s_armaz_ue, 
            SUM(case when armaz_us=1 then 1 else 0 end) s_armaz_us  
        from gpt_jobmov jm
        left join gpt_job j on jm.job_id=j.id
        where (j.id=j_id) or 
              (j.encomenda_id=e_id) or
              (j_id is null and e_id is null and j.ordem<>0)
        group by job_id
    )
    update gpt_job
    set corte_in=coalesce(s_corte_in,0),
        corte_out=coalesce(s_corte_out,0),
        linha_in=coalesce(s_linha_in,0),
        linha_out=coalesce(s_linha_out,0),
        armaz_in=coalesce(s_armaz_in,0),
        armaz_out=coalesce(s_armaz_out,0),
        data_ini=s_data_ini,
        data_fim=s_data_fim,
        delta=case 
            when s_armaz_us>=1 then s_armaz_out - qtd
            when s_armaz_ue>=1 then s_armaz_in  - qtd
            when s_linha_us>=1 then s_linha_out - qtd
            when s_linha_ue>=1 then s_linha_in  - qtd
            when s_corte_us>=1 then s_corte_out - qtd
            when s_corte_ue>=1 then s_corte_in  - qtd
            else 0
        end
    from tmp
    where gpt_job.id = tmp.job_id;

end
$$ LANGUAGE plpgsql;



CREATE OR REPLACE PROCEDURE gpt_j_jm_sync(j_id int, e_id int, target_j_id int) AS $$
begin
    /*with moved AS (
        insert into gpt_jobmov(job_id, data,
            corte_in,corte_ue,corte_out,corte_us,
            linha_in,linha_ue,linha_out,linha_us,
            armaz_in,armaz_ue,armaz_out,armaz_us)
        select id, current_date, 
            case when corte_in>qtd then corte_in-qtd else 0 end diff_corte_in, 0,
            case when corte_out>qtd then corte_out-qtd else 0 end diff_corte_out, 0,
            0, 0, 0, 0,
            0, 0, 0, 0
        from gpt_job j
        where( j.id=j_id or j.encomenda_id=e_id)
            and (corte_in>qtd or corte_out>qtd)
        returning  job_id, corte_in, corte_out
    )
    insert into gpt_jobmov(job_id,data,
        corte_in,corte_ue,corte_out,corte_us,
        linha_in,linha_ue,linha_out,linha_us,
        armaz_in,armaz_ue,armaz_out,armaz_us)
    select  target_j_id, current_date,
        sum(corte_in), 0, sum(corte_out), 0,
        0, 0, 0, 0,
        0, 0, 0, 0
    from moved;*/   

    with to_move as (
        select id, 
            case when corte_in>qtd then corte_in-qtd else 0 end diff_corte_in,
            case when corte_out>qtd then corte_out-qtd else 0 end diff_corte_out
        from gpt_job j
        where( j.id=j_id or j.encomenda_id=e_id)
            and (corte_in>qtd or corte_out>qtd) 
    ), moved as (
        insert into gpt_jobmov(job_id,data,
            corte_in,corte_ue,corte_out,corte_us,
            linha_in,linha_ue,linha_out,linha_us,
            armaz_in,armaz_ue,armaz_out,armaz_us)
        select id, current_date,
            -diff_corte_in, 0, -diff_corte_out, 0,
            0, 0, 0, 0,
            0, 0, 0, 0
        from to_move
        returning job_id, corte_in, corte_out
    )
    insert into gpt_jobmov(job_id,data,
        corte_in,corte_ue,corte_out,corte_us,
        linha_in,linha_ue,linha_out,linha_us,
        armaz_in,armaz_ue,armaz_out,armaz_us)
    select  target_j_id, current_date,
        sum(corte_in), 0, sum(corte_out), 0,
        0, 0, 0, 0,
        0, 0, 0, 0
    from moved; 

    call gpt_jm_j_sync();
end
$$ LANGUAGE plpgsql;



CREATE OR REPLACE PROCEDURE gpt_jobmov_post(
    _id int,
    _job_id int,
    _data date,
    _corte_in int,
    _corte_ue boolean,
    _corte_out int,
    _corte_us boolean,
    _linha_in int,
    _linha_ue boolean,
    _linha_out int,
    _linha_us boolean,
    _armaz_in int,
    _armaz_ue boolean,
    _armaz_out int,
    _armaz_us boolean) AS $$
begin
    if _id=0 then
        insert into job_mov(job_id,data,
            corte_in,corte_ue,corte_out,corte_us,
            linha_in,linha_ue,linha_out,linha_us,
            armaz_in,armaz_ue,armaz_out,armaz_us)
        values( _job_id, _data,
        _corte_in,_corte_ue,_corte_out,_corte_us,
        _linha_in,_linha_ue,_linha_out,_linha_us,
        _armaz_in,_armaz_ue,_armaz_out,_armaz_us
        );
    else
        update job_mov
        set data=_data,
            corte_in=_corte_in,
            corte_ue=_corte_ue,
            corte_out=_corte_out,
            corte_us=_corte_us,
            linha_in=_linha_in, 
            linha_ue=_linha_ue,
            linha_out=_linha_out,
            linha_us=_linha_us,
            armaz_in=_armaz_in,
            armaz_ue=_armaz_ue,
            armaz_out=_armaz_out,
            armaz_us=_armaz_us      
        where id=_id;
    end if;
end
$$ LANGUAGE plpgsql;

	
