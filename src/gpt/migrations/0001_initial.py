# Generated by Django 5.0.6 on 2024-07-08 14:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Encomenda',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ano', models.IntegerField(blank=True, null=True)),
                ('numero', models.IntegerField(blank=True, null=True)),
                ('ref', models.CharField(blank=True, max_length=20, null=True)),
                ('data', models.DateTimeField(blank=True, null=True)),
                ('data_limite', models.DateTimeField(blank=True, null=True)),
                ('estacao_id', models.IntegerField(blank=True, null=True)),
                ('estacao_ref', models.Char<PERSON>ield(blank=True, max_length=10, null=True)),
                ('cliente_ref', models.CharField(blank=True, max_length=20, null=True)),
                ('modelo_ref', models.CharField(blank=True, max_length=20, null=True)),
                ('modelo_nome', models.CharField(blank=True, max_length=50, null=True)),
                ('modelo_tempo_mins', models.IntegerField(blank=True, null=True)),
                ('modelo_preco_un', models.DecimalField(decimal_places=2, max_digits=10, null=True)),
                ('qtd', models.IntegerField(blank=True, null=True)),
                ('faltas', models.CharField(blank=True, max_length=50, null=True)),
                ('ok_data', models.DateTimeField(blank=True, null=True)),
                ('ok_producao', models.BooleanField(blank=True, null=True)),
                ('data_amostra', models.DateTimeField(blank=True, null=True)),
                ('data_ft', models.DateTimeField(blank=True, null=True)),
                ('data_pc', models.DateTimeField(blank=True, null=True)),
                ('data_molde', models.DateTimeField(blank=True, null=True)),
                ('data_okprod', models.DateTimeField(blank=True, null=True)),
                ('estadoid', models.IntegerField(choices=[(1, 'Open'), (0, 'Closed')], default=1)),
                ('obs', models.TextField(blank=True, null=True)),
                ('_data_prev', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Feriado',
            fields=[
                ('data', models.DateField(primary_key=True, serialize=False)),
                ('nome', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Linha',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=50)),
                ('n_pessoas', models.IntegerField()),
                ('dias_em_curso', models.IntegerField()),
                ('coeficiente', models.DecimalField(decimal_places=2, max_digits=5)),
                ('mins_dia', models.IntegerField()),
                ('activa', models.BooleanField(default=True)),
                ('externa', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Job',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('linha_n_pessoas', models.IntegerField(blank=True, null=True)),
                ('linha_coeficiente', models.FloatField(blank=True, null=True)),
                ('linha_mins_dia', models.IntegerField(blank=True, null=True)),
                ('linha_dias_em_curso', models.IntegerField(blank=True, null=True)),
                ('ordem', models.IntegerField(blank=True, null=True)),
                ('qtd', models.IntegerField(blank=True, null=True)),
                ('delta', models.IntegerField(blank=True, null=True)),
                ('corte_in', models.IntegerField(blank=True, null=True)),
                ('corte_out', models.IntegerField(blank=True, null=True)),
                ('linha_in', models.IntegerField(blank=True, null=True)),
                ('linha_out', models.IntegerField(blank=True, null=True)),
                ('armaz_in', models.IntegerField(blank=True, null=True)),
                ('armaz_out', models.IntegerField(blank=True, null=True)),
                ('data_ini', models.DateField(blank=True, null=True)),
                ('data_fim', models.DateField(blank=True, null=True)),
                ('_dias_prev', models.FloatField(blank=True, db_column='_dias_prev', null=True)),
                ('_dias_prev_acum', models.FloatField(blank=True, db_column='_dias_prev_acum', null=True)),
                ('_data_prev', models.DateTimeField(blank=True, db_column='_data_prev', null=True)),
                ('_qtd_dia', models.FloatField(blank=True, db_column='_qtd_dia', null=True)),
                ('encomenda', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gpt.encomenda')),
                ('linha', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='gpt.linha')),
            ],
        ),
        migrations.CreateModel(
            name='JobMov',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.DateField()),
                ('corte_in', models.IntegerField(blank=True, null=True)),
                ('corte_ue', models.BooleanField(blank=True, null=True)),
                ('corte_out', models.IntegerField(blank=True, null=True)),
                ('corte_us', models.BooleanField(blank=True, null=True)),
                ('linha_in', models.IntegerField(blank=True, null=True)),
                ('linha_ue', models.BooleanField(blank=True, null=True)),
                ('linha_out', models.IntegerField(blank=True, null=True)),
                ('linha_us', models.BooleanField(blank=True, null=True)),
                ('armaz_in', models.IntegerField(blank=True, null=True)),
                ('armaz_ue', models.BooleanField(blank=True, null=True)),
                ('armaz_out', models.IntegerField(blank=True, null=True)),
                ('armaz_us', models.BooleanField(blank=True, null=True)),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='gpt.job')),
            ],
        ),
    ]
