from django.db import models



#
#  M A N A G E R S
#

class EncomendaManager(models.Manager):
    def with_stats(self):
        return self.raw('select * from gpt_v_encomenda')




#
#  M O D E L S
#


class Feriado(models.Model):
    data = models.DateField(primary_key=True)
    nome = models.CharField(max_length=50)

    def __str__(self):
        return f'{self.data} {self.nome}'        


class Linha(models.Model):
    nome = models.CharField(max_length=50)
    n_pessoas = models.IntegerField()
    dias_em_curso = models.IntegerField()
    coeficiente = models.DecimalField(max_digits=5, decimal_places=2)
    mins_dia = models.IntegerField()

    activa = models.BooleanField(default=True)
    externa = models.BooleanField(default=False)

    def __str__(self):
        return f'{self.id} {self.name}'       


class EncomendaEstado(models.IntegerChoices):
    OPEN = 1, 'Open'
    CLOSED = 0, 'Closed'

class Encomenda(models.Model):
    objects = EncomendaManager()

    ano = models.IntegerField(blank=True, null=True)
    numero = models.IntegerField(blank=True, null=True)
    ref = models.CharField(max_length=20, blank=True, null=True)
    data = models.DateTimeField(blank=True, null=True)
    data_limite = models.DateTimeField(blank=True, null=True)
    estacao_id = models.IntegerField(blank=True, null=True)
    estacao_ref = models.CharField(max_length=10, blank=True, null=True)
    cliente_ref = models.CharField(max_length=20, blank=True, null=True)
    
    modelo_ref = models.CharField(max_length=20, blank=True, null=True)
    modelo_nome = models.CharField(max_length=50, blank=True, null=True)
    modelo_tempo_mins = models.IntegerField(blank=True, null=True)
    modelo_preco_un = models.DecimalField(max_digits=10, decimal_places=2, null=True)
    
    qtd = models.IntegerField(blank=True, null=True)
    faltas = models.CharField(max_length=50, blank=True, null=True)
    
    ok_data = models.DateTimeField(blank=True, null=True)
    ok_producao = models.BooleanField(blank=True, null=True)
    data_amostra = models.DateTimeField(blank=True, null=True)
    data_ft = models.DateTimeField(blank=True, null=True)
    data_pc = models.DateTimeField(blank=True, null=True)
    data_molde = models.DateTimeField(blank=True, null=True)
    data_okprod = models.DateTimeField(blank=True, null=True)
    
    estadoid = models.IntegerField(choices=EncomendaEstado.choices, default=EncomendaEstado.OPEN)
    
    obs = models.TextField(blank=True, null=True)
    
    _data_prev = models.DateTimeField(blank=True, null=True)

    def __str__(self) -> str:
        return f'{self.ano}/{self.numero} {self.ref}'


class Job(models.Model):
    encomenda = models.ForeignKey('Encomenda', on_delete=models.CASCADE)
    linha = models.ForeignKey('Linha', on_delete=models.PROTECT)
    linha_n_pessoas = models.IntegerField(blank=True, null=True)
    linha_coeficiente = models.FloatField(blank=True, null=True)
    linha_mins_dia = models.IntegerField(blank=True, null=True)
    linha_dias_em_curso = models.IntegerField(blank=True, null=True)
    ordem = models.IntegerField(blank=True, null=True)
    qtd = models.IntegerField(blank=True, null=True)
    delta = models.IntegerField(blank=True, null=True)
    corte_in = models.IntegerField(blank=True, null=True)
    corte_out = models.IntegerField(blank=True, null=True)
    linha_in = models.IntegerField(blank=True, null=True)
    linha_out = models.IntegerField(blank=True, null=True)
    armaz_in = models.IntegerField(blank=True, null=True)
    armaz_out = models.IntegerField(blank=True, null=True)
    data_ini = models.DateField(blank=True, null=True)
    data_fim = models.DateField(blank=True, null=True)
    
    _dias_prev = models.FloatField(db_column='_dias_prev', blank=True, null=True)  
    _dias_prev_acum = models.FloatField(db_column='_dias_prev_acum', blank=True, null=True)
    _data_prev = models.DateTimeField(db_column='_data_prev', blank=True, null=True)
    _qtd_dia = models.FloatField(db_column='_qtd_dia', blank=True, null=True)

    def __str__(self) -> str:
        return f'{self.encomenda} {self.linha}'


class JobMov(models.Model):
    job = models.ForeignKey('Job', on_delete=models.CASCADE)
    data = models.DateField()
    corte_in = models.IntegerField(blank=True, null=True)
    corte_ue = models.BooleanField(blank=True, null=True)
    corte_out = models.IntegerField(blank=True, null=True)
    corte_us = models.BooleanField(blank=True, null=True)
    linha_in = models.IntegerField(blank=True, null=True)
    linha_ue = models.BooleanField(blank=True, null=True)
    linha_out = models.IntegerField(blank=True, null=True)
    linha_us = models.BooleanField(blank=True, null=True)
    armaz_in = models.IntegerField(blank=True, null=True)
    armaz_ue = models.BooleanField(blank=True, null=True)
    armaz_out = models.IntegerField(blank=True, null=True)
    armaz_us = models.BooleanField(blank=True, null=True)