"""
custom lookups for orm field lookups
ccontains
icontains, istartswith, iendswith are redifined to use ILIKE
instead of UPPER() LIKE UPPER()
ILIKE uses the indexes provided by pg_trgm module from postgres
and speed increases by more than 100x

ex:
CREATE EXTENSION pg_trgm;
create index common_cpostal_nome_trgm on common_cpostal using gin (nome gin_trgm_ops);
"""


from django.db.models.lookups import Lookup
from django.db.models.fields import Field

from django.conf import settings

if settings.DATABASES['default']['ENGINE'] == 'django.db.backends.postgresql':

    @Field.register_lookup
    class alxContains(Lookup):
        lookup_name = 'ccontains'
        prepare_rhs = False
        param_pattern = '%{}%'

        def as_sql(self, compiler, connection):
            lhs_sql, params = self.process_lhs(compiler, connection)
            lhs_sql = connection.ops.lookup_cast('contains') % lhs_sql

            rhs_sql, rhs_params = self.process_rhs(compiler, connection)
            if rhs_params and not self.bilateral_transforms:
                s = connection.ops.prep_for_like_query(rhs_params[0])
                rhs_params[0] = self.param_pattern.format(s.replace(' ', '% %'))
            params.extend(rhs_params)
            # rhs_sql = connection.operators['icontains'] % rhs_sql
            rhs_sql = 'ILIKE %s' % rhs_sql
            return '%s %s' % (lhs_sql, rhs_sql), params


    @Field.register_lookup
    class alxIContains(alxContains):
        lookup_name = 'icontains'

    @Field.register_lookup
    class alxIStartsWith(alxContains):
        lookup_name = 'istartswith'
        param_pattern = '{}%'

    @Field.register_lookup
    class alxIEndsWith(alxContains):
        lookup_name = 'iendswith'
        param_pattern = '%{}'
