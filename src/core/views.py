from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.http import HttpResponse
from django import forms


def main_view(request):
    return render(request, 'core/main.html')

from core.validators import validate_vat_number


class LoginForm(forms.Form):
    username = forms.CharField(max_length=10, validators=[validate_vat_number], label='Utilizador')
    password = forms.CharField(max_length=10, label='Palavra-passe', widget=forms.PasswordInput)


def main_view(request):
    return render(request, 'core/main.html')


def history_view(request):
    return render(request, 'core/public_history.html')


def login_view(request):
    form = LoginForm(request.POST or None)
    if request.method == 'POST':
        if form.is_valid():
            username = form.cleaned_data.get("username")
            password = form.cleaned_data.get("password")
            user = authenticate(request, username=username, password=password)
            if user:
                login(request, user)
                response = HttpResponse('', status=204)
                response.headers["HX-Redirect"] = '/'
                return response
    context = {
        'form': form,
    }
    return render(request, 'core/login.html', context)


def logout_view(request):
    logout(request)
    return redirect('/')


def profile_view(request):
    return render(request, template_name='core/profile.html')