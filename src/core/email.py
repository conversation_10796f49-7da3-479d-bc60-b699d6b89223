import logging

from django.conf import settings
from django.core.mail import EmailMessage, SafeMIMEText
from pathlib import Path

logger = logging.getLogger(__name__)

class EmailHtml(EmailMessage):
    cids = {}

    def attach_html(self, html: str):
        """html to include in message"""
        encoding = self.encoding or settings.DEFAULT_CHARSET
        self.attach(SafeMIMEText(html, "html", encoding))

    def attach_file_cid(self, path, cid, mimetype=None):
        """
        Attaches a file from the filesystem, with a Content-ID.
        For use with multipart/related messages.
        """
        super().attach_file(path, mimetype)
        path = Path(path)
        self.cids[path.name] = cid

    def _create_attachment(self, filename, content, mimetype=None):
        attachment = super()._create_attachment(filename, content, mimetype)
        if filename in self.cids:
            attachment.add_header('Content-ID', self.cids[filename])
        return attachment

    def send(self, fail_silently=False):
        if settings.DEBUG:
            self.to = ['<EMAIL>']
            self.cc = []
            self.bcc = []
        logger.debug(f'EMAIL sending subject:{self.subject} to:{self.to} cc:{self.cc} bcc:{self.bcc}')
        super().send(fail_silently)
