<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="INA">
    <title>INA</title>
    <link rel="stylesheet" href="/static/pure-min.css">
    <link rel="stylesheet" href="/static/grids-responsive-min.css">
    <link rel="stylesheet" href="/static/side-menu.css">
    <link rel="stylesheet" href="/static/font-awesome.css">
    <link rel="stylesheet" href="/static/my.css">
</head>
<body  hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'>

<div id="layout">
    <!-- Menu toggle -->
    <a href="#menu" id="menuLink" class="menu-link">
        <!-- Hamburger icon -->
        <span></span>
    </a>

    <div id="menu">
        <div class="pure-menu">
            <a href="/" style="margin: 0; padding: 0;"><img src="/static/img/laranjeiras.png" width="100%"></a>
            {% if user.is_authenticated %}
            <ul class="pure-menu-list">
                <img src="{% if user.terceiro.foto %}{{user.terceiro.foto.url}}{% else %}/satic/user.jpg{% endif %}"" style="height:60px; border-radius:50%;">
                <li class="pure-menu-item"><a href="{% url 'core:logout' %}" class="pure-menu-link">Logout</a></li>
                <li class="pure-menu-item"><a href="#" hx-get="{% url 'core:profile' %}" hx-target="#content" class="pure-menu-link">Perfil</a></li>
            </ul>
            {% endif %}


            <ul class="pure-menu-list">
                <li class="pure-menu-item"><a href="#" hx-get="{% url 'condo:new_despesas_ct' %}" hx-target="#content" class="pure-menu-link">Despesas</a></li>
                <li class="pure-menu-item"><a href="#" hx-get="{% url 'condo:new_receitas_ct' %}" hx-target="#content" class="pure-menu-link">Receitas</a></li>
                <li class="pure-menu-item"><a href="#" hx-get="{% url 'condo:banco_movimentos_monthly' %}" hx-target="#content" class="pure-menu-link">Banco Movimentos</a></li>


                <li class="pure-menu-item"><a href="#" hx-get="{% url 'noapp:wz1' %}" hx-target="#content" class="pure-menu-link">Wizard form</a></li>
                <li class="pure-menu-item"><a href="#" hx-get="{% url 'noapp:wz2' 2 %}" hx-target="#content" class="pure-menu-link">Wizard model Form</a></li>
            </ul>



            {% if user.is_authenticated %}
            <ul class="pure-menu-list">
                <li class="pure-menu-item"><a href="#" hx-get="{% url 'ws:cantina' %}" hx-target="#content" class="pure-menu-link">Refeições</a></li>
                {% if user.is_colaborador and user.escola == 'APINA' %}
                <li class="pure-menu-item"><a href="#" hx-get="{% url 'bercario:home' %}" hx-target="#content" class="pure-menu-link">Berçário</a></li>
                {% endif %}
                {% if user.is_colaborador %}
                <li class="pure-menu-item"><a href="#" hx-get="{% url 'reembolsos2:home' %}" hx-target="#content" class="pure-menu-link">Reembolsos</a></li>
                {% endif %}
                {% if user.is_df %}
                <li class="pure-menu-item"><a href="#" hx-get="{% url 'reembolsos2:globalstate' %}" hx-target="#content" class="pure-menu-link">Reembolsos DF</a></li>
                {% endif %}
            {% endif %}

        </div>
    </div>

    <div id="main">
        <div class="header">
            <h1>CondoApp</h1>
            <h2>Bem-vindo ao sistema de gestão de condomínio</h2>
        </div>

        <div id="content">
            {% if user.is_authenticated %}
                <div hx-get="/profile/" hx-trigger="load" hx-swap="outerHTML"></div>
            {% else %}
                <div hx-get="/login/" hx-trigger="load" hx-swap="outerHTML"></div>
            {% endif %}
        </div>

        <div class="footer">
            © 2025 CondoApp - Todos os Direitos Reservados
            <span style="font-size:60%;">optimizado para Google Chrome</span>
        </div>        
    </div>
</div>

<!-- Modal dialog -->
<div id="document-modal" class="modal" style="display: none;">
  <div class="modal-overlay" onclick="closeModal()"></div>
  <div class="modal-container">
    <div class="modal-header">
      <h3 id="modal-title">Documento</h3>
      <button class="modal-close" onclick="closeModal()">×</button>
    </div>
    <div id="modal-content" class="modal-content">
      <!-- Content will be loaded here via HTMX -->
    </div>
  </div>
</div>

<script>
  function openModal() {
    document.getElementById('document-modal').style.display = 'flex';
    document.body.style.overflow = 'hidden'; // Prevent scrolling
  }
  
  function closeModal() {
    document.getElementById('document-modal').style.display = 'none';
    document.body.style.overflow = 'auto'; // Restore scrolling
  }
  
  // Close modal on ESC key
  document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
      closeModal();
    }
  });
</script>

<script src="/static/ui.js"></script>
<script src="/static/htmx.min.js"></script>

</body>
</html>