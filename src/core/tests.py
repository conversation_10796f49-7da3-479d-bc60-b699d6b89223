from django.test import TestCase
from datetime import datetime

# Create your tests here.
from django.test import TestCase

from .validators import is_iban, is_bic_code, is_iban_bic, is_vat_number, is_phone, is_citizen_card
from .extenso import *
from .strutils import *
from .cron import *


class ValidatorsTestCase(TestCase):
    # fixtures = ['core.txt']

    def test_iban(self):
        self.assertTrue(is_iban('****************************'))
        self.assertTrue(is_iban('************************'))
        self.assertTrue(is_iban('********************'))
        self.assertTrue(is_iban('****************************'))
        self.assertTrue(is_iban('**********************'))
        self.assertTrue(is_iban('****************************'))
        self.assertTrue(is_iban('****************'))
        self.assertTrue(is_iban('********************'))
        self.assertTrue(is_iban('*****************************'))
        self.assertTrue(is_iban('**********************'))
        self.assertTrue(is_iban('**********************'))
        self.assertTrue(is_iban('*********************'))
        self.assertTrue(is_iban('****************************'))
        self.assertTrue(is_iban('************************'))
        self.assertTrue(is_iban('******************'))
        self.assertTrue(is_iban('****************************'))
        self.assertTrue(is_iban('*****************************'))
        self.assertTrue(is_iban('****************************'))
        self.assertTrue(is_iban('********************'))
        self.assertTrue(is_iban('******************'))
        self.assertTrue(is_iban('******************'))
        self.assertTrue(is_iban('***************************'))
        self.assertTrue(is_iban('**********************'))
        self.assertTrue(is_iban('**********************'))
        self.assertTrue(is_iban('***********************'))
        self.assertTrue(is_iban('***************************'))
        self.assertTrue(is_iban('******************'))
        self.assertTrue(is_iban('****************************'))
        self.assertTrue(is_iban('**********************'))
        self.assertTrue(is_iban('****************************'))
        self.assertTrue(is_iban('**************************'))
        self.assertTrue(is_iban('***********************'))
        self.assertTrue(is_iban('**********************'))
        self.assertTrue(is_iban('***********************'))
        self.assertTrue(is_iban('***************************'))
        self.assertTrue(is_iban('******************************'))
        self.assertTrue(is_iban('********************'))
        self.assertTrue(is_iban('******************************'))
        self.assertTrue(is_iban('****************************'))
        self.assertTrue(is_iban('********************'))
        self.assertTrue(is_iban('*******************************'))
        self.assertTrue(is_iban('******************************'))
        self.assertTrue(is_iban('***************************'))
        self.assertTrue(is_iban('**********************'))
        self.assertTrue(is_iban('******************'))
        self.assertTrue(is_iban('*******************'))
        self.assertTrue(is_iban('***************'))
        self.assertTrue(is_iban('************************'))
        self.assertTrue(is_iban('*****************************'))
        self.assertTrue(is_iban('****************************'))
        self.assertTrue(is_iban('*************************'))
        self.assertTrue(is_iban('*****************************'))
        self.assertTrue(is_iban('************************'))
        self.assertTrue(is_iban('********************************'))
        self.assertTrue(is_iban('***************************'))
        self.assertTrue(is_iban('*************************'))
        self.assertTrue(is_iban('************************'))
        self.assertTrue(is_iban('**********************'))
        self.assertTrue(is_iban('*******************************'))
        self.assertTrue(is_iban('************************'))
        self.assertTrue(is_iban('*******************'))
        self.assertTrue(is_iban('************************'))
        self.assertTrue(is_iban('************************'))
        self.assertTrue(is_iban('*********************'))
        self.assertTrue(is_iban('***********************'))
        self.assertTrue(is_iban('************************'))
        self.assertTrue(is_iban('**************************'))
        self.assertTrue(is_iban('*****************************'))
        self.assertTrue(is_iban('***********************'))
        self.assertTrue(is_iban('**********************'))
        self.assertTrue(is_iban('************************'))
        self.assertTrue(is_iban('PT50 0007 0000 0025 4473 7782 3'))
        self.assertFalse(is_iban('PT50 0007 0000 0025 4473 7782 4'))

    def test_bic_code(self):
        self.assertTrue(is_bic_code('BGALPTTG'))
        self.assertTrue(is_bic_code('BESCPTPL'))

    def test_iban_bic(self):
        self.assertTrue(is_iban_bic('PT50 0007 0000 0025 4473 7782 3', 'BESCPTPL'))
        self.assertFalse(is_iban_bic('PT50 0007 0000 0025 4473 7782 3', 'BGALPTTG'))

    def test_vat_number(self):
        self.assertTrue(is_vat_number('190502762'))
        self.assertTrue(is_vat_number('PT190502762'))
        self.assertTrue(is_vat_number('PT209092238'))
        self.assertFalse(is_vat_number('190502763'))

    def test_phone(self):
        self.assertTrue(is_phone('912687323'))
        self.assertTrue(is_phone('+351 912687323'))
        self.assertFalse(is_phone('+351 9126873230'))

    def test_cc(self):
        self.assertTrue(is_citizen_card('08102880 6 ZZ2'))
        self.assertFalse(is_citizen_card('08102880 6 ZZ3'))


class ExtensoTestCase(TestCase):
    def test_extenso(self):
        d = {
            0:          'zero',
            1:          'um',
            21:         'vinte e um',
            321:        'trezentos e vinte e um',
            4321:       'quatro mil, trezentos e vinte e um',
            54321:      'cinquenta e quatro mil, trezentos e vinte e um',
            654321:     'seiscentos e cinquenta e quatro mil, trezentos e vinte e um',
            7654321:    'sete milhões, seiscentos e cinquenta e quatro mil, trezentos e vinte e um',
            87654321:   'oitenta e sete milhões, seiscentos e cinquenta e quatro mil, trezentos e vinte e um',
            987654321:  'novecentos e oitenta e sete milhões, seiscentos e cinquenta e quatro mil, '
                        'trezentos e vinte e um',
            1987654321: 'um bilião, novecentos e oitenta e sete milhões, seiscentos e cinquenta e quatro mil, '
                        'trezentos e vinte e um',


            1099:    'mil e noventa e nove',
            25800:   'vinte e cinco mil e oitocentos',
            25019:   'vinte e cinco mil e dezanove',
            4600819: 'quatro milhões e seiscentos mil, oitocentos e dezanove',
            4025819: 'quatro milhões e vinte e cinco mil, oitocentos e dezanove',
            4025800: 'quatro milhões e vinte e cinco mil e oitocentos',
            4600019: 'quatro milhões e seiscentos mil e dezanove',

        }

        for k, v in d.items():
            self.assertEqual(extenso(k), v)


class StringTestCase(TestCase):
    def test_silabas(self):
        d = {
            'pai':          'pai',
            'lá':           'lá',
            'mãe':          'mãe',
            'vês':          'vês',
            'fui':          'fui',
            'milhão':       'mi-lhão',
            'milhões':      'mi-lhões',
            'manhã':        'ma-nhã',
            'alexandre':    'a-le-xan-dre',
            # 'amo-te':       'a-mo-te',
            'ptolomeu':     'pto-lo-meu',
            'abdicar':      'ab-di-car',
            'diagnóstico':  'dia-gnós-ti-co'
        }
        for k, v in d.items():
            self.assertEqual('-'.join(silabas(k)), v)

    def test_abrev(self):
        d = {
            'Matemática': 'Mat.',
            'Inglês': 'Ing.',
            'Português': 'Port.',
            'Controlo de qualidade': 'CQ',
            'Doutor': 'Dr.',
            'Engenheiro': 'Eng.'
        }
        for k, v in d.items():
            self.assertEqual(abrev(k), v)

    def test_abrev_name(self):
        d = {
            'António Alexandre Cunha Monteiro Rua': 'António Rua',
            'Regina Cunha': 'Regina Cunha',
            'Alex': 'Alex',
        }
        for k, v in d.items():
            self.assertEqual(abrev_name(k), v)

        d = {
            'António Alexandre Cunha Monteiro Rua': 'António Alexandre Rua',
            'Regina Cunha': 'Regina Cunha',
        }

        for k, v in d.items():
            self.assertEqual(abrev_name(k, 3), v)

    def test_sigla(self):
        d = {
            'pequenas e médias empresas': 'PME',
            'fisica e quimica': 'FQ',
            'fisico quimica':    'FQ',
            'paises africanos de lingua oficial portuguesa': 'PALOP',
            'Instituto nacional de estatística': 'INE',
            'Instituto nacional de emergência médica': 'INEM',
        }
        for k, v in d.items():
            self.assertEqual(sigla(k), v)


class CronTestCase(TestCase):
    data_ref = datetime(2024, 1, 1, 8, 0) 

    def next_10_schedules(self, calendar_exp):
        ret =[]
        d = self.data_ref
        for _ in range(10):
            d = get_next_execution(calendar_exp, d)
            ret.append(d)
        return ret

    def test_cron(self):
        examples = {
            # a cada 5 minutos da hora
            "*:00/5": [
                datetime(2024,1,1,8,5),
                datetime(2024,1,1,8,10),
                datetime(2024,1,1,8,15),
                datetime(2024,1,1,8,20),
                datetime(2024,1,1,8,25),
                datetime(2024,1,1,8,30),
                datetime(2024,1,1,8,35),
                datetime(2024,1,1,8,40),
                datetime(2024,1,1,8,45),
                datetime(2024,1,1,8,50)
            ],
            # todas as sextas feiras
            "Fri 08:00": [
                datetime(2024,1,5,8,0),
                datetime(2024,1,12,8,0),
                datetime(2024,1,19,8,0),
                datetime(2024,1,26,8,0),
                datetime(2024,2,2,8,0),
                datetime(2024,2,9,8,0),
                datetime(2024,2,16,8,0),
                datetime(2024,2,23,8,0),
                datetime(2024,3,1,8,0),
                datetime(2024,3,8,8,0),
            ],
            # todos os dias 25 de cada mês 
            "25 8:00": [
                datetime(2024,1,25,8,0),
                datetime(2024,2,25,8,0),
                datetime(2024,3,25,8,0),
                datetime(2024,4,25,8,0),
                datetime(2024,5,25,8,0),
                datetime(2024,6,25,8,0),
                datetime(2024,7,25,8,0),
                datetime(2024,8,25,8,0),
                datetime(2024,9,25,8,0),
                datetime(2024,10,25,8,0),
            ], 
            # sexta feira 13
            "Fri 13 9:00": [
                datetime(2024,9,13,9,0),
                datetime(2024,12,13,9,0),
                datetime(2025,6,13,9,0),
                datetime(2026,2,13,9,0),
                datetime(2026,3,13,9,0),
                datetime(2026,11,13,9,0),
                datetime(2027,8,13,9,0),
                datetime(2028,10,13,9,0),
                datetime(2029,4,13,9,0),
                datetime(2029,7,13,9,0),
            ], 
            # nos primeros 3 dias de cada mes
            "1..3 9:00": [
                datetime(2024,1,1,9,0),
                datetime(2024,1,2,9,0),
                datetime(2024,1,3,9,0),
                datetime(2024,2,1,9,0),
                datetime(2024,2,2,9,0),
                datetime(2024,2,3,9,0),
                datetime(2024,3,1,9,0),
                datetime(2024,3,2,9,0),
                datetime(2024,3,3,9,0),
                datetime(2024,4,1,9,0),
            ],
            # primeira 4 feira de cada mes
            "wed 1..7 9:00": [
                datetime(2024,1,3,9,0),
                datetime(2024,2,7,9,0),
                datetime(2024,3,6,9,0),
                datetime(2024,4,3,9,0),
                datetime(2024,5,1,9,0),
                datetime(2024,6,5,9,0),
                datetime(2024,7,3,9,0),
                datetime(2024,8,7,9,0),
                datetime(2024,9,4,9,0),
                datetime(2024,10,2,9,0),
            ]
        }

        for k,v in examples.items():
            self.assertEqual(v, self.next_10_schedules(k))