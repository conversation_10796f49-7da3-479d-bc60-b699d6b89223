from decimal import Decimal

from PIL import Image
from io import BytesIO

from django.db import models
from django.db.models.fields.files import <PERSON>Field, ImageFieldFile
from django.core.files.base import ContentFile

from core.validators import (
    validate_iban, validate_bic_code, 
    validate_vat_number, validate_niss,
    validate_phone
)

class NameField(models.CharField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('max_length', 100)
        super(NameField, self).__init__(*args, **kwargs)


class AddressField(models.CharField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('max_length', 150)
        super(AddressField, self).__init__(*args, **kwargs)


class CurrencyField(models.DecimalField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('decimal_places', 2)
        kwargs.setdefault('max_digits', 10)
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self). __init__(*args, **kwargs)

    def to_python(self, value):
        try:
            return super(CurrencyField, self).to_python(value).quantize(Decimal("0.01"))
        except AttributeError:
            return None


class RTFField(models.TextField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('blank', True)
        super(RTFField, self).__init__(*args, **kwargs)


class VATNOField(models.CharField):
    default_validators = [validate_vat_number]

    def __init__(self, *args, **kwargs):
        kwargs['max_length'] = 15
        super(VATNOField, self).__init__(*args, **kwargs)


class IBANField(models.CharField):
    default_validators = [validate_iban]

    def __init__(self, *args, **kwargs):
        kwargs['max_length'] = 34
        super(IBANField, self).__init__(*args, **kwargs)


class BICField(models.CharField):
    default_validators = [validate_bic_code]

    def __init__(self, *args, **kwargs):
        kwargs['max_length'] = 11
        super(BICField, self).__init__(*args, **kwargs)


class PhoneField(models.CharField):
    default_validators = [validate_phone]

    def __init__(self, *args, **kwargs):
        kwargs['max_length'] = 20
        super(PhoneField, self).__init__(*args, **kwargs)


class NISSField(models.CharField):
	default_validators = [validate_niss]
    
	def __init__(self, *args, **kwargs):
		kwargs.setdefault('max_length', 11)
		super(NISSField, self).__init__(*args, **kwargs)	

# ThumbnailImageField

class ThumbnailImageFieldFile(ImageFieldFile):
    
    def save(self, name, content, save=True):
        new_content = BytesIO()
        content.file.seek(0)
        img = Image.open(content.file)
        img.thumbnail(self.field.size, Image.LANCZOS)
        img.save(new_content, format='JPEG')
        new_content = ContentFile(new_content.getvalue())
        super(ThumbnailImageFieldFile, self).save(name, new_content, save)


class ThumbnailImageField(ImageField):
    attr_class = ThumbnailImageFieldFile

    def __init__(self, *args, size, **kwargs):
        self.size = size
        super().__init__(*args, **kwargs)

    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        kwargs['size'] = self.size
        return name, path, args, kwargs