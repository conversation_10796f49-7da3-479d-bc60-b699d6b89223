import json
from typing import Any
from urllib.parse import urlsplit
from urllib.parse import urlunsplit

from urllib.parse import unquote

from django.http import HttpRequest
from django.http.response import HttpResponseBase
from django.utils.functional import cached_property


class HtmxDetails:
    def __init__(self, request: HttpRequest) -> None:
        self.request = request
        self.notice_list = []
        self.trigger_list = {}

    def _get_header_value(self, name: str) -> str | None:
        value = self.request.headers.get(name) or None
        if value:
            if self.request.headers.get(f"{name}-URI-AutoEncoded") == "true":
                value = unquote(value)
        return value

    def __bool__(self) -> bool:
        return self._get_header_value("HX-Request") == "true"

    @cached_property
    def boosted(self) -> bool:
        return self._get_header_value("HX-Boosted") == "true"

    @cached_property
    def current_url(self) -> str | None:
        return self._get_header_value("HX-Current-URL")

    @cached_property
    def current_url_abs_path(self) -> str | None:
        url = self.current_url
        if url is not None:
            split = urlsplit(url)
            if (
                split.scheme == self.request.scheme
                and split.netloc == self.request.get_host()
            ):
                url = urlunsplit(split._replace(scheme="", netloc=""))
            else:
                url = None
        return url

    @cached_property
    def history_restore_request(self) -> bool:
        return self._get_header_value("HX-History-Restore-Request") == "true"

    @cached_property
    def prompt(self) -> str | None:
        return self._get_header_value("HX-Prompt")

    @cached_property
    def target(self) -> str | None:
        return self._get_header_value("HX-Target")

    @cached_property
    def trigger(self) -> str | None:
        return self._get_header_value("HX-Trigger")

    @cached_property
    def trigger_name(self) -> str | None:
        return self._get_header_value("HX-Trigger-Name")

    @cached_property
    def triggering_event(self) -> Any:
        value = self._get_header_value("Triggering-Event")
        if value is not None:
            try:
                value = json.loads(value)
            except json.JSONDecodeError:
                value = None
        return value

    def add_notice(self, title='', text='', image='', sticky=False) -> None:
        d = {}
        if title: d['title'] = title
        if text: d['text'] = text
        if image: d['image'] = image
        if sticky: d['sticky'] = sticky
        self.notice_list.append(d)

    def add_trigger(self, name:str, payload:dict|None=None) ->None:
        self.trigger_list[name] = payload if payload else True

    def update_response(self, response:HttpResponseBase) -> None:
        self.trigger_list['notices'] = self.notice_list
        response.headers['HX-Trigger'] = json.dumps(self.trigger_list)


def add_notice(request: HttpRequest, title='', text='', image='', sticky=False) -> None:
    request.htmx.add_notice(title, text, image, sticky)

    
def add_trigger(request: HttpRequest, name:str, payload:dict|None=None) ->None:
    request.htmx.add_trigger(name, payload)
