from django.db import models

# Create your models here.

class BGTask(models.Model):
    PENDING = 0
    IN_PROGRESS = 1
    COMPLETED = 2

    STATUS_CHOICES = [
        (PENDING, 'Pending'),
        (IN_PROGRESS, 'In Progress'),
        (COMPLETED, 'Completed'),
    ]

    function = models.CharField(max_length=100)
    parameters = models.JSONField()
    status = models.SmallIntegerField(choices=STATUS_CHOICES, default=PENDING)
    created = models.DateTimeField(auto_now_add=True)
    executed = models.DateTimeField(auto_now=True)
    error = models.TextField(null=True)

    class Meta:
        indexes = [
            models.Index(fields=['status'])
        ]

    def __str__(self) -> str:
        return f"<{self.function}> {'ERROR' if self.error else ''}"