"""
conversão de numeros para extenso em português

http://www.professornews.com.br/index.php/utilidades/dicas-de-redacao/7357-como-ler-e-escrever-numerais-cardinais

Regra geral para leitura e grafia de numerais cardinais
Por convenção, as classes de números (unidades simples, milhares, milhões etc.)
são separadas por vírgulas, com a conjunção -e- ligando as unidades, dezenas e
centenas dentro de cada classe.

A leitura do número 1.000 é "mil".
O número "um mil" não existe. Somente a partir do número 2.000 (dois mil),
a leitura é feita expressando o algarismo que vem antes do mil.

Leitura e grafia (regra geral)
==============================

1               um
21              vinte e um
321             trezentos e vinte e um
4.321           quatro mil, trezentos e vinte e um
54.321          cinquenta e quatro mil, trezentos e vinte e um
654.321         seiscentos e cinquenta e quatro mil, trezentos e vinte e um
7.654.321       sete milhões, seiscentos e cinquenta e quatro mil, trezentos e vinte e um
87.654.321      oitenta e sete milhões, seiscentos e cinquenta e quatro mil,
                trezentos e vinte e um
987.654.321     novecentos e oitenta e sete milhões, seiscentos e cinquenta e quatro mil,
                trezentos e vinte e um
1.987.654.321   um bilião, novecentos e oitenta e sete milhões,
                seiscentos e cinquenta e quatro mil, trezentos e vinte e um

Exceções à regra geral
Quando a centena da classe anterior é um número redondo (por exemplo, 300)
ou inicia-se com zero (por exemplo, 025),
utiliza-se a conjunção e para ligar uma classe de números e outra.


Leitura e grafia (exceções à regra geral)
=========================================
1.099       mil e noventa e nove
25.800      vinte e cinco mil e oitocentos
25.019      vinte e cinco mil e dezanove
4.600.819   quatro milhões e seiscentos mil, oitocentos e dezenove
4.025.819   quatro milhões e vinte e cinco mil, oitocentos e dezenove
4.025.800   quatro milhões e vinte e cinco mil e oitocentos
4.600.019   quatro milhões e seiscentos mil e dezenove

"""


unidades = ',um,dois,três,quatro,cinco,seis,sete,oito,nove'\
           ',dez,onze,doze,treze,quatorze,quinze,dezaseis,dezasete,dezoito,dezanove'.split(',')
dezenas = ',,vinte,trinta,quarenta,cinquenta,sessenta,setenta,oitenta,noventa'.split(',')
centenas = ',cento,duzentos,trezentos,quatrocentos,quinhentos,seiscentos,setecentos,oitocentos,novecentos'.split(',')


def cdu(number):
    def join(s1, s2):
        if s1 and s2:
            return s1+' e '+s2
        else:
            return s1 + s2

    if number == 100:
        return 'cem'
    c, du = divmod(number, 100)
    rv = centenas[c]
    if du < 20:
        return join(rv, unidades[du])
    d, u = divmod(du, 10)
    return join(rv, join(dezenas[d], unidades[u]))


def extenso(n):

    def join(s1, s2, lastn):
        if s2 == '':
            return s1
        elif (lastn < 100) or (last_n % 100 == 0):
            return s1 + ' e ' + s2
        else:
            return s1 + ', ' + s2

    if n == 0:
        return 'zero'

    s = str(n)
    if s.find('.') == -1:
        s += '.0'
    i, f = s.split('.')
    i, f = int(i), int(f)

    rv = ''
    last_n = 0

    if i >= 0:  # centenas
        i, n = divmod(i, 1000)
        rv = cdu(n)
        last_n = n

    if i > 0:  # milhares
        if i == 1:
            return join('mil', rv, last_n)
        i, n = divmod(i, 1000)
        rv = join(cdu(n) + ' mil', rv, last_n)
        last_n = n

    if i > 0:  # milhoes
        if i == 1:
            return join('um milhão', rv, last_n)
        i, n = divmod(i, 1000)
        rv = join(cdu(n) + ' milhões', rv, last_n)
        last_n = n

    if i > 0:  # biliões
        if i == 1:
            return join('um bilião', rv, last_n)
        i, n = divmod(i, 1000)
        rv = join(cdu(n) + ' biliões', rv, last_n)
        last_n = n

    return rv
