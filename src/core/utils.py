
from collections import defaultdict
import colorsys

from django.db.models import Model


#
#  DB/Model
#


def get_property_by_name(instance: Model, property_name: str):
    """
    Retrieves a property by name from a Django model instance.
    Supports field lookup syntax with double underscores for related objects.
    """
    parts = property_name.split('__')
    current_value = instance
    for part in parts:
        current_value = getattr(current_value, part)
        if current_value is None:
            break
    return current_value


def group_queryset(queryset, field_path):
    """
    Groups a queryset by a field path using Django's field lookup syntax.
    
    Args:
        queryset: The Django queryset to group
        field_path: String with field path using Django's lookup syntax (e.g. 'fracao__proprietario')
    
    Returns:
        A defaultdict with objects grouped by the specified field
    """
    
    parts = field_path.split('__')
    grouped_data = defaultdict(list)
    for obj in queryset:
        current_value = obj
        for part in parts:
            current_value = getattr(current_value, part)        
            if current_value is None:
                break
        grouped_data[current_value].append(obj)
    return grouped_data



#
#  Colors
#

def generate_html_colors(n):
    """Generate `n` distinct HTML colors."""
    colors = []
    for i in range(n):
        hue = i / n  # Evenly space hues around the color wheel
        rgb = colorsys.hsv_to_rgb(hue, 0.8, 1)  # Use 80% saturation and full brightness
        hex_color = "#{:02X}{:02X}{:02X}".format(
            int(rgb[0] * 255), int(rgb[1] * 255), int(rgb[2] * 255)
        )
        colors.append(hex_color)
    return colors

def calculate_luminance(hex_color):
    """Calculate the relative luminance of an RGB color."""
    r, g, b = int(hex_color[1:3], 16), int(hex_color[3:5], 16), int(hex_color[5:7], 16)
    # Convert RGB to linear RGB
    def linearize(channel):
        channel /= 255
        return channel / 12.92 if channel <= 0.03928 else ((channel + 0.055) / 1.055) ** 2.4
    r, g, b = linearize(r), linearize(g), linearize(b)
    # Relative luminance formula
    return 0.2126 * r + 0.7152 * g + 0.0722 * b

def get_contrast_font_color(hex_color):
    """Choose white or black font color for best contrast."""
    luminance = calculate_luminance(hex_color)
    return "#000000" if luminance > 0.5 else "#FFFFFF"
