
import re

"""
Designa-se por sigla a abreviatura de uma expressão constituída
pelo conjunto dos carateres iniciais dos elementos que compõem essa expressão.
As siglas escrevem-se com carateres maiúsculos e geralmente sem pontos.
Em regra, não se representam artigos, preposições, conjunções nem advérbios na sigla.
"""


def sigla(text):
    words = [w for w in text.split() if len(w) > 3]
    if len(words) == 1:
        return abrev(words[0])
    return ''.join([w[0].upper() for w in words])


"""
As abreviaturas são, em regra, constituídas pelas primeiras letras
da palavra representada e terminam na primeira consoante da sílaba
a partir da qual se faz a eliminação do resto da palavra.
São geralmente rematadas por ponto final.
"""

# algumas abreviaturas fora das regras
common_abrevs = {
    'Engenheiro': 'Eng.',
    'Doutor': 'Dr.',
    '<PERSON><PERSON>ra': 'Dra.',
}


def abrev(word):
    if word in common_abrevs:
        return common_abrevs[word]
    if len(word) <= 3:
        return word
    if ' ' in word:
        return sigla(word)
    sils = silabas(word)
    if len(sils) == 1:
        return sils[0]
    rv = sils[0]
    for letter in sils[1]:
        rv += letter
        if letter in 'bsdfghjklmnpqrstvwxyz':
            break
    return rv + '.'


def abrev_name(name, names=2):
    bits = name.split()
    if len(bits) > names:
        return ' '.join(bits[:names - 1]) + ' ' + bits[-1]

    return ' '.join(bits)


# ----------------------------------------------------------------------------
# dividir uma palavra em silabas

# http://cvc.instituto-camoes.pt/cpp/acessibilidade/capitulo6_1.html
# http://www.trucosos.com/snippets/206/

cre = re.compile(
    u'''(b|br|bl|c|ç|ch|cr|cl|d|dr|f|fr|fl|gu|g|
    gr|gl|gn|h|j|k|kr|kl|l|ll|lh|m|mn|n|nh|p|ph|
    pr|pl|pt|qu|q|rr|r|s|t|tr|tl|v|vr|vl|w|x|y|z)?
    (ih?u(?![aeoáéíóú])|
    # preámbulo, posibles principios de sílaba.
    # - i, h opcional y u, si detrás de la u no viene una vocal fuerte
    #   porque si viniera, la u se uniría a la vocal fuerte en la sílaba
    #   siguiente, como en chihuahua.
    # Ejemplos:
    #   viu-da, pi-pihu-ca (eso no existe, pero si existiera, se dividiría así)
    # no encuentro ninguno con consonante detrás de la y

    ão|ãe|õe|
    [iu]?[aeoáéíóúêâã](h?[iuy](?![aeoiuáéíóú]))?|
    # - Este es el caso fundamental, el de la mayoría de las sílabas.
    #   Este caso maneja las sílabas compuestas de solamente una vocal fuerte,
    #   los diptongos débil-fuerte y fuerte débil, y los triptongos.
    #   La estructura es: una vocal débil opcional, una fuerte y otra
    #   débil opcional. La presencia de las vocales débiles determina
    #   si hay diptongo, triptongo o nada. Una h puede aparecer entre la fuerte
    #   y la segunda vocal débil. No así entre la primera débil y la fuerte,
    #   pues ... VERIFICAR, POSIBLE ERROR!
    ui|[ui]|
    y(?![aeiouáéíóúãõ]))''',
    re.UNICODE | re.IGNORECASE | re.VERBOSE)


def silabas(word):
    pos = []
    for m in cre.finditer(word):
        pos.append(m.start())
    pos.append(len(word))
    return [word[pos[x]:pos[x + 1]] for x in range(len(pos) - 1)]
