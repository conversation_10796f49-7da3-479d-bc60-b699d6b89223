from django.utils import timezone
from datetime import datetime


cronjob_registry = []

class CronJob:
    def __init__(self, name:str, calendar_exp:str, func:callable) -> None:
        self.name = name
        self.calendar_exp = normalize_calendar_exp(calendar_exp)
        self.func = func
        self.update_next_run()
    
    def update_next_run(self):
        self.next_run = get_next_execution(self.calendar_exp)


def register_cron_job(name:str, calendar_exp:str, func) -> None:
    cronjob_registry.append(CronJob(name, calendar_exp, func))


"""
       Calendar events may be used to refer to one or more points in time in a single expression. They form a superset of the absolute timestamps
       explained above:

           Thu,Fri 2012-*-1,5 11:12:13

       The above refers to 11:12:13 of the first or fifth day of any month of the year 2012, but only if that day is a Thursday or Friday.

       The weekday specification is optional. If specified, it should consist of one or more English language weekday names, either in the abbreviated
       (Wed) or non-abbreviated (Wednesday) form (case does not matter), separated by commas. Specifying two weekdays separated by ".."  refers to a
       range of continuous weekdays.  "," and ".."  may be combined freely.

       In the date and time specifications, any component may be specified as "*" in which case any value will match. Alternatively, each component can
       be specified as a list of values separated by commas. Values may be suffixed with "/" and a repetition value, which indicates that the value
       itself and the value plus all multiples of the repetition value are matched. Two values separated by ".."  may be used to indicate a range of
       values; ranges may also be followed with "/" and a repetition value, in which case the expression matches all times starting with the start
       value, and continuing with all multiples of the repetition value relative to the start value, ending at the end value the latest.

       A date specification may use "~" to indicate the last day in a month. For example, "*-02~03" means "the third last day in February," and "Mon
       *-05~07/1" means "the last Monday in May."

       The seconds component may contain decimal fractions both in the value and the repetition. All fractions are rounded to 6 decimal places.

       Either time or date specification may be omitted, in which case *-*-* and 00:00:00 is implied, respectively. If the seconds component is not
       specified, ":00" is assumed.

       Timezone can be specified as the literal string "UTC", or the local timezone, similar to the supported syntax of timestamps (see above), or the
       timezone in the IANA timezone database format (also see above).

       The following special expressions may be used as shorthands for longer normalized forms:

               minutely → *-*-* *:*:00
                 hourly → *-*-* *:00:00
                  daily → *-*-* 00:00:00
                monthly → *-*-01 00:00:00
                 weekly → Mon *-*-* 00:00:00
                 yearly → *-01-01 00:00:00
              quarterly → *-01,04,07,10-01 00:00:00
           semiannually → *-01,07-01 00:00:00

       Examples for valid timestamps and their normalized form:

             Sat,Thu,Mon..Wed,Sat..Sun → Mon..Thu,Sat,Sun *-*-* 00:00:00
                 Mon,Sun 12-*-* 2,1:23 → Mon,Sun 2012-*-* 01,02:23:00
                               Wed *-1 → Wed *-*-01 00:00:00
                      Wed..Wed,Wed *-1 → Wed *-*-01 00:00:00
                            Wed, 17:48 → Wed *-*-* 17:48:00
           Wed..Sat,Tue 12-10-15 1:2:3 → Tue..Sat 2012-10-15 01:02:03
                           *-*-7 0:0:0 → *-*-07 00:00:00
                                 10-15 → *-10-15 00:00:00
                   monday *-12-* 17:00 → Mon *-12-* 17:00:00
             Mon,Fri *-*-3,1,2 *:30:45 → Mon,Fri *-*-01,02,03 *:30:45
                  12,14,13,12:20,10,30 → *-*-* 12,13,14:10,20,30:00
                       12..14:10,20,30 → *-*-* 12..14:10,20,30:00
             mon,fri *-1/2-1,3 *:30:45 → Mon,Fri *-01/2-01,03 *:30:45
                        03-05 08:05:40 → *-03-05 08:05:40
                              08:05:40 → *-*-* 08:05:40
                                 05:40 → *-*-* 05:40:00
                Sat,Sun 12-05 08:05:40 → Sat,Sun *-12-05 08:05:40
                      Sat,Sun 08:05:40 → Sat,Sun *-*-* 08:05:40
                      2003-03-05 05:40 → 2003-03-05 05:40:00
            05:40:23.4200004/3.1700005 → *-*-* 05:40:23.420000/3.170001
                        2003-02..04-05 → 2003-02..04-05 00:00:00
                  2003-03-05 05:40 UTC → 2003-03-05 05:40:00 UTC
                            2003-03-05 → 2003-03-05 00:00:00
                                 03-05 → *-03-05 00:00:00
                                hourly → *-*-* *:00:00
                                 daily → *-*-* 00:00:00
                             daily UTC → *-*-* 00:00:00 UTC
                               monthly → *-*-01 00:00:00
                                weekly → Mon *-*-* 00:00:00
               weekly Pacific/Auckland → Mon *-*-* 00:00:00 Pacific/Auckland
                                yearly → *-01-01 00:00:00
                              annually → *-01-01 00:00:00
                                 *:2/3 → *-*-* *:02/3:00

"""


shorthands = {
    "minutely": "*-*-* *:*:00",
    "hourly": "*-*-* *:00:00",
    "daily": "*-*-* 00:00:00",
    "monthly": "*-*-01 00:00:00",
    "weekly": "Mon *-*-* 00:00:00",
    "yearly": "*-01-01 00:00:00",
    "quarterly": "*-01,04,07,10-01 00:00:00",
    "semiannually": "*-01,07-01 00:00:00"
}

dows = ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]
months = ["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"]


def normalize_calendar_exp(exp):
    def _(s, n=2):
        if s == "*":
            return s
        return s.zfill(n)

    exp = exp.strip().lower()
    if exp in shorthands:
        return shorthands[exp]
    
    parts = exp.split()
    weekday_spec = ""
    ymd_spec = ""
    time_spec = ""
    unknown = ""

    for part in parts:
        if "-" in part:
            ymd_spec = part
        elif ":" in part:
            time_spec = part
        elif any([dow in part for dow in dows]):
            weekday_spec = part
        else:
            unknown = part
    if unknown:
        if not time_spec:
            time_spec = unknown
        elif not ymd_spec:
            ymd_spec = unknown
               
    # simplify/correct parts
    if not weekday_spec:
        weekday_spec ="*"
    else:
        tmp = set()
        parts = weekday_spec.split(',')
        for part in parts:
            if ".." in part:
                w1, w2 = part.split("..")
                tmp.update(dows[dows.index(w1):dows.index(w2)+1])
            else:
                tmp.add(part)
        weekday_lst = []
        for dow in dows:
            if dow in tmp:
                weekday_lst.append(dow)
        weekday_spec = ",".join(weekday_lst)

    if not ymd_spec:
        ymd_spec = "*-*-*"
    else:
        match ymd_spec.split("-"):
            case [y, m, d]:
                ymd_spec = f'{_(y,4)}-{_(m)}-{_(d)}'
            case [m, d]:
                ymd_spec = f'*-{_(m)}-{_(d)}'
            case [d]:
                ymd_spec = f'*-*-{_(d)}'

    if not time_spec:
        time_spec = "00:00:00"
    else:
        match time_spec.split(":"):
            case [h, m, s]:
                time_spec = f'{_(h)}:{_(m)}:{_(s)}'
            case [h, m]:
                time_spec = f'{_(h)}:{_(m)}:00'
            case [h]:
                time_spec = f'{_(h)}:00:00'

    return f"{weekday_spec} {ymd_spec} {time_spec}"



def spec_to_list(spec, min, max, options_lst=None):
    vals = []
    parts = spec.split(',')
    for part in parts:
        step = 1
        if '/' in part:
            part, step = part.split('/')
            if step.isnumeric():
                step = int(step)
        if '..' in part:
            left, right = part.split('..')
            if left == '':
                left = min
            elif left.isnumeric():
                left = int(left)
            else:
                left = options_lst.index(left.lower()) + 1
            
            if right == '':
                right = max
            elif right.isnumeric():
                right = int(right)
            else:
                right = options_lst.index(right.lower()) + 1            
        elif part == '*':
            left = min
            right = max
        elif part.isnumeric():
            left = int(part)
            if step == 1:
                right = left
            else:
                right = max
        else:
            left = options_lst.index(part.lower()) + 1
            right = left
        vals.extend([n for n in range(left,right+1,step)])

    return sorted(list(set(vals)))



def get_next_execution(calendar_exp, now=None):
    calendar_exp = normalize_calendar_exp(calendar_exp)

    weekday_spec, ymd_spec, time_spec = calendar_exp.split()
    year_spec, month_spec, day_spec = ymd_spec.split("-")
    hour_spec, minute_spec, _ = time_spec.split(":")

    if now is None:
        now = datetime.now()  
    # now = timezone.now()
    current_year = now.year
    current_month = now.month
    current_day = now.day
    current_hour = now.hour
    current_minute = now.minute

    dow_list = spec_to_list(weekday_spec, 1, 7, dows)
    year_list = spec_to_list(year_spec, current_year, current_year+20)
    month_list = spec_to_list(month_spec, 1, 12, months)
    day_list = spec_to_list(day_spec, 1, 31)
    hour_list = spec_to_list(hour_spec, 0, 23)
    minute_list = spec_to_list(minute_spec, 0, 59)
    # print("dow   ", dow_list)
    # print("year  ", year_list)
    # print("month ", month_list)
    # print("day   ", day_list)
    # print("hour  ", hour_list)
    # print("minute", minute_list)


    # Iterate through all possible future dates and times
    year_list = [y for y in year_list if y>=current_year]
    for year in year_list:
        for month in month_list:
            if year == current_year and month < current_month: 
                continue
            for day in day_list:
                if year == current_year and month == current_month and day < current_day: 
                    continue
                try:
                    d = datetime(year, month, day)
                    if d.isoweekday() in dow_list:
                        for hour in hour_list:
                            for minute in minute_list:
                                d = datetime(year, month, day, hour, minute)
                                # d = datetime(year, month, day, hour, minute, tzinfo=timezone.utc)
                                if d > now:
                                    return d
                except:
                    # handle invalid dates ex days 31 on 30 or 28 days months
                    pass
                            
    # If no suitable datetime is found
    return None    





if __name__ == "__main__":
    d = {
        # "Sat,Mon..Wed,Sat..Sun": "Mon..Wed,Sat,Sun *-*-* 00:00:00",
        # "Mon,Sun 2025-*-* 2,1:23": "Mon,Sun 2025-*-* 01,02:23:00",
        # "Mon,Sun 2024-*-* 2,1:23": "Mon,Sun 2024-*-* 01,02:23:00",
        # "Mon,Sun 2023-*-* 2,1:23": "Mon,Sun 2024-*-* 01,02:23:00",
        #"Wed *-1": "Wed *-*-01 00:00:00",
        # "Wed..Wed,Wed *-1": "Wed *-*-01 00:00:00",
        # "Wed, 17:48": "Wed *-*-* 17:48:00",
        # "Wed..Sat,Tue 12-10-15 1:2:3": "Tue..Sat 2012-10-15 01:02:03",
        # "*-*-7 0:0:0": "*-*-07 00:00:00",
        # "10-15": "*-10-15 00:00:00",
        # "mon *-12-* 17:00": "Mon *-12-* 17:00:00",
        # "mon,fri *-1/2-1,3 8:30:45": "Mon,Fri *-01/2-01,03 08:30:45",
        # "03-05 08:05:40": "*-03-05 08:05:40"
        "*:00/5": "* *-*-* *:00/5:00",  # a cda 5 minutos da hora
        "Fri 08:00": "", # todas as sextas feiras
        "25 8:00": "", # todos os dias 25 de cada mês
    }

    for k,v in d.items():
        ne = normalize_calendar_exp(k)
        print(k, "\n", v, "\n", ne)
        d = None
        for _ in range(20):
            d = get_next_execution(ne, d)
            print(d)


