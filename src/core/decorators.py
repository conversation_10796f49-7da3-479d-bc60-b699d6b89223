from typing import Any

from .models import BGTask
from .cron import register_cron_job


class task():
    def __init__(self, func) -> None:
        self.func = func
        self.func_name = f'{func.__module__}.{func.__name__}'
        
    def __call__(self, *args: Any, **kwargs: Any) -> Any:
        BGTask.objects.create(
            function=self.func_name,
            parameters={"args": args, "kwargs": kwargs}
        )

    def original(self, *args: Any, **kwargs: Any) -> Any:
        return self.func(*args, **kwargs)
    

# def task(func):
#     def bg_func(*args, **kwargs):
#         BGTask.objects.create(
#             function=f'{func.__module__}.{func.__name__}',
#             parameters={"args": args, "kwargs": kwargs}
#         )
#     func.bg = bg_func
#     return func

class cronjob():
    def __init__(self, name, schedule):
        self.name = name
        self.schedule = schedule

    def __call__(self, func):
        register_cron_job(self.name, self.schedule, func)
        return func