
_specs = {
    'PT': r'[0-9]{8}[0-9][0-9A-Z]{2}[0-9]',
}


def pt_valid(val):
    """
    O número  de  documento  é  constituído  por  4  elementos distintos,  sendo  que  se  encontra
    visível na parte frontal do Cartão de Cidadão.
    Formato: DDDDDDDD C AAT
    Onde:
        D - Número de Identificação Civil [0.. 9]
        C – Check Digit do Número de Identificação Civil [0.. 9]
        A – Versão [A.. Z, 0.. 9]
        T – Check Digit Número de Documento [0.. 9]

    Validacao
    0. converter as letras A=10, B=1,... Z=35
    1. Efectuando uma contagem da direita para a esquerda
    duplicar o valor de cada 2º elemento
    2. No caso do resultado da duplicação ser igual ou superior a 10, subtrair 9 ao seu valor;
    3. Somar a totalidade dos valores obtidos;
    4. Ao valor obtido, deve-se calcular o resto da sua divisão por 10,
    se o valor for 0 o número de documento é válido.
    """
    if not len(val) == 12:
        return False
    lst = [int(x, 36) for x in val]
    t = 0
    for i, x in enumerate(lst):
        if i % 2 == 0:
            x *= 2
            if x >= 10:
                x -= 9
        t += x
    return (t % 10) == 0
