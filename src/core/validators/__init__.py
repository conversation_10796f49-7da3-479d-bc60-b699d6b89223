from django.core.exceptions import ValidationError

from ..constants import COUNTRY_CODES

from . import citizencard
from . import vatnumber
from . import iban
from . import phone
from . import niss

from .phone import *


def is_f_factory(module):
    def _intf(val):
        val = val.replace(' ', '').upper()
        cc = val[:2]
        if not cc.isalpha():
            val = "PT" + val
        cc = val[:2]
        num = val[2:]
        if cc not in COUNTRY_CODES:
            return False
        specs = getattr(module, '_specs', None)
        if specs and cc in specs:
            if not re.fullmatch(specs[cc], num, re.VERBOSE):
                return False
        func = getattr(module, cc.lower()+'_valid', None)
        if func:
            return func(num)
        func = getattr(module, 'valid', None)
        if func:
            return func(val)
        return True
    return _intf


is_citizen_card = is_f_factory(citizencard)
is_vat_number = is_f_factory(vatnumber)
is_niss = is_f_factory(niss)
is_iban = is_f_factory(iban)
is_bic_code = iban.is_bic_code
is_iban_bic = iban.is_iban_bic
is_phone = phone.is_phone


def validation_factory(is_f, message):
    def _intf(*val):
        if not is_f(*val):
            raise ValidationError(message) from None
    return _intf


validate_citizen_card = validation_factory(is_citizen_card, 'Número de cartao de cidadao inválido')
validate_vat_number = validation_factory(is_vat_number, 'NIF (Número de identificação fiscal) inválido')
validate_niss = validation_factory(is_niss, 'NISS (Número de identificação da segurança social) inválido')
validate_iban = validation_factory(is_iban, 'IBAN (Número de identificação bancária internacional) inválido')
validate_bic_code = validation_factory(is_bic_code, 'BIC (Business Identifier Code) inválido')
validate_iban_bic = validation_factory(is_iban_bic, 'O IBAN e o BIC não estão em concordância')
validate_phone = validation_factory(is_phone, 'Número de telefone inválido')
