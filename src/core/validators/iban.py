from ..constants import COUNTRY_CODES

_specs = {
    'AL': '[0-9]{2}[0-9]{8}[a-z,A-Z,0-9]{16}',
    'DZ': '[0-9]{2}[0-9]{22}',
    'AD': '[0-9]{2}[0-9]{8}[a-z,A-Z,0-9]{12}',
    'AO': '[0-9]{2}[0-9]{21}',
    'AT': '[0-9]{2}[0-9]{16}',
    'AZ': '[0-9]{2}[a-z,A-Z,0-9]{4}[0-9]{20}',
    'BH': '[0-9]{2}[A-Z]{4}[a-z,A-Z,0-9]{14}',
    'BY': '[0-9]{2}[a-z,A-Z,0-9]{4}[0-9]{4}[a-z,A-Z,0-9]{16}',
    'BE': '[0-9]{2}[0-9]{12}',
    'BJ': '[0-9]{2}[a-z,A-Z,0-9]{2}[0-9]{22}',
    'BA': '[0-9]{2}[0-9]{16}',
    'BR': '[0-9]{2}[0-9]{23}[A-Z]{1}[a-z,A-Z,0-9]{1}',
    'BG': '[0-9]{2}[A-Z]{4}[0-9]{6}[a-z,A-Z,0-9]{8}',
    'BF': '[0-9]{2}[a-z,A-Z,0-9]{2}[0-9]{22}',
    'BI': '[0-9]{2}[0-9]{12}',
    'CV': '[0-9]{2}[0-9]{21}',
    'CM': '[0-9]{2}[0-9]{23}',
    'CF': '[0-9]{2}[0-9]{23}',
    'TD': '[0-9]{2}[0-9]{23}',
    'KM': '[0-9]{2}[0-9]{23}',
    'CG': '[0-9]{2}[0-9]{23}',
    'CR': '[0-9]{2}[0-9]{18}',
    'HR': '[0-9]{2}[0-9]{17}',
    'CY': '[0-9]{2}[0-9]{8}[a-z,A-Z,0-9]{16}',
    'CZ': '[0-9]{2}[0-9]{20}',
    'CI': '[0-9]{2}[A-Z]{1}[0-9]{23}',
    'DK': '[0-9]{2}[0-9]{14}',
    'DJ': '[0-9]{2}[0-9]{23}',
    'DO': '[0-9]{2}[A-Z]{4}[0-9]{20}',
    'EG': '[0-9]{2}[0-9]{25}',
    'SV': '[0-9]{2}[A-Z]{4}[0-9]{20}',
    'GQ': '[0-9]{2}[0-9]{23}',
    'EE': '[0-9]{2}[0-9]{16}',
    'FO': '[0-9]{2}[0-9]{14}',
    'FI': '[0-9]{2}[0-9]{14}',
    'FR': '[0-9]{2}[0-9]{10}[a-z,A-Z,0-9]{11}[0-9]{2}',
    'GA': '[0-9]{2}[0-9]{23}',
    'GE': '[0-9]{2}[a-z,A-Z,0-9]{2}[0-9]{16}',
    'DE': '[0-9]{2}[0-9]{18}',
    'GI': '[0-9]{2}[A-Z]{4}[a-z,A-Z,0-9]{15}',
    'GR': '[0-9]{2}[0-9]{7}[a-z,A-Z,0-9]{16}',
    'GL': '[0-9]{2}[0-9]{14}',
    'GT': '[0-9]{2}[a-z,A-Z,0-9]{4}[a-z,A-Z,0-9]{20}',
    'GW': '[0-9]{2}[a-z,A-Z,0-9]{2}[0-9]{19}',
    'VA': '[0-9]{2}[0-9]{3}[0-9]{15}',
    'HN': '[0-9]{2}[A-Z]{4}[0-9]{20}',
    'HU': '[0-9]{2}[0-9]{24}',
    'IS': '[0-9]{2}[0-9]{22}',
    'IR': '[0-9]{2}[a-z,A-Z,0-9]{4}[0-9]{18}',
    'IQ': '[0-9]{2}[A-Z]{4}[0-9]{15}',
    'IE': '[0-9]{2}[a-z,A-Z,0-9]{4}[0-9]{14}',
    'IL': '[0-9]{2}[0-9]{19}',
    'IT': '[0-9]{2}[A-Z]{1}[0-9]{10}[a-z,A-Z,0-9]{12}',
    'JO': '[0-9]{2}[A-Z]{4}[0-9]{22}',
    'KZ': '[0-9]{2}[0-9]{3}[a-z,A-Z,0-9]{13}',
    'KW': '[0-9]{2}[A-Z]{4}[a-z,A-Z,0-9]{22}',
    'LV': '[0-9]{2}[A-Z]{4}[a-z,A-Z,0-9]{13}',
    'LB': '[0-9]{2}[0-9]{4}[a-z,A-Z,0-9]{20}',
    'LY': '[0-9]{2}[0-9]{21}',
    'LI': '[0-9]{2}[0-9]{5}[a-z,A-Z,0-9]{12}',
    'LT': '[0-9]{2}[0-9]{16}',
    'LU': '[0-9]{2}[0-9]{3}[a-z,A-Z,0-9]{13}',
    'MG': '[0-9]{2}[0-9]{23}',
    'ML': '[0-9]{2}[a-z,A-Z,0-9]{2}[0-9]{22}',
    'MT': '[0-9]{2}[A-Z]{4}[0-9]{5}[a-z,A-Z,0-9]{18}',
    'MR': '[0-9]{2}[0-9]{23}',
    'MU': '[0-9]{2}[A-Z]{4}[0-9]{19}[A-Z]{3}',
    'MD': '[0-9]{2}[a-z,A-Z,0-9]{2}[a-z,A-Z,0-9]{18}',
    'MC': '[0-9]{2}[0-9]{10}[a-z,A-Z,0-9]{11}[0-9]{2}',
    'ME': '[0-9]{2}[0-9]{18}',
    'MA': '[0-9]{2}[0-9]{24}',
    'MZ': '[0-9]{2}[0-9]{21}',
    'NL': '[0-9]{2}[A-Z]{4}[0-9]{10}',
    'NI': '[0-9]{2}[A-Z]{4}[0-9]{24}',
    'NE': '[0-9]{2}[A-Z]{2}[0-9]{22}',
    'NO': '[0-9]{2}[0-9]{11}',
    'PK': '[0-9]{2}[a-z,A-Z,0-9]{4}[0-9]{16}',
    'PS': '[0-9]{2}[a-z,A-Z,0-9]{4}[0-9]{21}',
    'PL': '[0-9]{2}[0-9]{24}',
    'PT': '[0-9]{2}[0-9]{21}',
    'QA': '[0-9]{2}[A-Z]{4}[a-z,A-Z,0-9]{21}',
    'MK': '[0-9]{2}[0-9]{3}[a-z,A-Z,0-9]{10}[0-9]{2}',
    'RO': '[0-9]{2}[A-Z]{4}[a-z,A-Z,0-9]{16}',
    'LC': '[0-9]{2}[A-Z]{4}[a-z,A-Z,0-9]{24}',
    'SM': '[0-9]{2}[A-Z]{1}[0-9]{10}[a-z,A-Z,0-9]{12}',
    'ST': '[0-9]{2}[0-9]{21}',
    'SA': '[0-9]{2}[0-9]{2}[a-z,A-Z,0-9]{18}',
    'SN': '[0-9]{2}[A-Z]{1}[0-9]{23}',
    'RS': '[0-9]{2}[0-9]{18}',
    'SC': '[0-9]{2}[A-Z]{4}[0-9]{20}[A-Z]{3}',
    'SK': '[0-9]{2}[0-9]{20}',
    'SI': '[0-9]{2}[0-9]{15}',
    'ES': '[0-9]{2}[0-9]{20}',
    'SE': '[0-9]{2}[0-9]{20}',
    'CH': '[0-9]{2}[0-9]{5}[a-z,A-Z,0-9]{12}',
    'TL': '[0-9]{2}[0-9]{19}',
    'TG': '[0-9]{2}[A-Z]{2}[0-9]{22}',
    'TN': '[0-9]{2}[0-9]{20}',
    'TR': '[0-9]{2}[0-9]{5}[a-z,A-Z,0-9]{17}',
    'UA': '[0-9]{2}[0-9]{6}[a-z,A-Z,0-9]{19}',
    'AE': '[0-9]{2}[0-9]{3}[0-9]{16}',
    'GB': '[0-9]{2}[A-Z]{4}[0-9]{14}',
    'VG': '[0-9]{2}[a-z,A-Z,0-9]{4}[0-9]{16}',
}


def valid(val):
    """
    https://en.wikipedia.org/wiki/International_Bank_Account_Number
    The IBAN consists of up to 34 alphanumeric characters, as follows:

    country code using ISO 3166-1 alpha-2 - two letters,
    check digits - two digits, and
    Basic Bank Account Number (BBAN) - up to 30 alphanumeric characters that are country-specific

    Validating the IBAN

    An IBAN is validated by converting it into an integer and performing a basic mod-97 operation
    (as described in ISO 7064) on it. If the IBAN is valid, the remainder equals 1.
    The algorithm of IBAN validation is as follows:

        1. Check that the total IBAN length is correct as per the country.
           If not, the IBAN is invalid
        2. Move the four initial characters to the end of the string
        3. Replace each letter in the string with two digits, thereby expanding the string,
           where A = 10, B = 11, ..., Z = 35
        4. Interpret the string as a decimal integer and compute the remainder of that number on division by 97
           If the remainder is 1, the check digit test is passed and the IBAN might be valid.

    """

    val = val[4:] + val[:4]
    val = ''.join([str(int(c, 36)) for c in val])
    return (int(val) % 97) == 1


def is_bic_code(val):
    """
    https://en.wikipedia.org/wiki/ISO_9362
    The SWIFT code is 8 or 11 characters, made up of:

    4 letters: Institution Code or bank code.
    2 letters: ISO 3166-1 alpha-2 country code
    2 letters or digits: location code
    3 letters or digits: branch code, optional ('619' for primary office)
    """
    val = val.replace(' ', '')
    return (
        len(val) in (8, 11) and
        val[4:6] in COUNTRY_CODES
    )


# https://www.bportugal.pt/pt-PT/pagamentos/SEPA/RegrasdeFuncionamento
# /Documents/SEPA%20-%20COD%20e%20BIC%20dos%20PSP-pt.pdf
BICS_PT = {
    '0001': ('BGALPTTG', 'BANCO DE PORTUGAL, EP'),
    '0007': ('BESCPTPL', 'BANCO ESPIRITO SANTO, SA'),
    '0008': ('BAIPPTPL', 'BANCO BAI EUROPA, SA'),
    '0010': ('BBPIPTPL', 'BANCO BPI, SA'),
    '0014': ('IVVSPTPL', 'BANCO INVEST, SA'),
    '0018': ('TOTAPTPL', 'BANCO SANTANDER TOTTA, SA'),
    '0019': ('BBVAPTPL', 'BANCO BILBAO VIZCAYA ARGENTARIA (PORTUGAL), SA'),
    '0022': ('BRASPTPL', 'BANCO DO BRASIL AG – SUCURSAL EM PORTUGAL'),
    '0023': ('ACTVPTP1', 'BANCO ACTIVOBANK, SA'),
    '0025': ('CXBIPTPL', 'CAIXA – BANCO DE INVESTIMENTO, SA'),
    '0027': ('BPIPPTPL', 'BANCO PORTUGUÊS DE INVESTIMENTO, SA'),
    '0032': ('BARCPTPL', 'BARCLAYS BANK, PLC'),
    '0033': ('BCOMPTPL', 'BANCO COMERCIAL PORTUGUÊS, SA'),
    '0035': ('CGDIPTPL', 'CAIXA GERAL DE DEPÓSITOS, SA'),
    '0036': ('MPIOPTPL', 'CAIXA ECONÓMICA MONTEPIO GERAL'),
    '0038': ('BNIFPTPL', 'BANIF – BANCO INTERNACIONAL DO FUNCHAL, SA'),
    '0045': ('CCCMPTPL', 'SISTEMA INTEGRADO DO CRÉDITO AGRÍCOLA MÚTUO (SICAM)'),
    '0046': ('CRBNPTPL', 'BANCO POPULAR PORTUGAL, SA'),
    '0047': ('ESSIPTPL', 'BANCO ESPÍRITO SANTO DE INVESTIMENTO, SA'),
    '0048': ('BFIAPTPL', 'BANCO FINANTIA, SA'),
    '0059': ('CEMAPTP2', 'CAIXA ECONÓMICA DA MISERICÓRDIA DE ANGRA DO HEROÍSMO'),
    '0061': ('BDIGPTPL', 'BANCO DE INVESTIMENTO GLOBAL, SA'),
    '0063': ('BNFIPTPL', 'BANIF – BANCO DE INVESTIMENTO, SA'),
    '0064': ('BPGPPTPL', 'BANCO PORTUGUÊS DE GESTÃO, SA'),
    '0065': ('BESZPTPL', 'BEST – BANCO ELECTRÓNICO DE SERVIÇO TOTAL, SA'),
    '0073': ('IBNBPTP1', 'BANCO SANTANDER CONSUMER PORTUGAL, SA'),
    '0079': ('BPNPPTPL', 'BANCO BIC PORTUGUÊS, SA'),
    '0086': ('EFISPTPL', 'BANCO EFISA, SA'),
    '0097': ('CCCHPTP1', 'CAIXA DE CRÉDITO AGRÍCOLA MÚTUO DA CHAMUSCA, CRL'),
    '0098': ('CERTPTP1', 'CAIXA DE CRÉDITO AGRÍCOLA MÚTUO DE BOMBARRAL, CRL'),
    '0099': ('CSSOPTPX', 'BANCO DE CAJA DE ESPAÑA DE INVERSIONES, SALAMANCA Y SORIA, SA – SUCURSAL EM PORTUGAL'),
    '0160': ('BESAPTPA', 'BANCO ESPÍRITO SANTO DOS AÇORES, SA'),
    '0186': ('CFESPTPL', 'BANQUE PRIVÉE ESPÍRITO SANTO, SA  ‐  SUCURSAL EM PORTUGAL'),
    '0189': ('BAPAPTPL', 'BANCO PRIVADO ATLÂNTICO – EUROPA, SA'),
    '0235': ('CAOEPTP1', 'BANCO L. J. CARREGOSA, SA'),
    '0244': ('MPCGPTP1', 'BANCO GRUPO CAJATRES, SA – SUCURSAL EM PORTUGAL'),
    '0698': ('UIFCPTP1', 'UNICRE  ‐  INSTITUIÇÃO FINANCEIRA DE CRÉDITO, SA'),
    '0781': ('IGCPPTPL', 'AGÊNCIA DE GESTÃO DA TESOURARIA E DA DÍVIDA PÚBLICA – IGCP, E.P.E.'),
    '5180': ('CDCTPTP2', 'CAIXA DE CRÉDITO AGRÍCOLA MÚTUO DE LEIRIA, CRL'),
    '5200': ('CDOTPTP1', 'CAIXA DE CRÉDITO AGRÍCOLA MÚTUO DE MAFRA, CRL'),
    '5340': ('CTIUPTP1', 'CAIXA DE CRÉDITO AGRÍCOLA MÚTUO DE TORRES VEDRAS, CRL'),
    # ('(2)', 'BBPIFRPP', 'BANCO BPI – SUCURSAL PARIS'),
    # ('(2)', 'CGDIESMM', 'BANCO CAIXA GERAL'),
    # ('(2)', 'BNIFMTMT', 'BANIF MALTA'),
    # ('(2)', 'BESMESMM', 'BES – ESPANHA'),
    # ('(2)', 'CGDILUL1', 'C.G.D. LUXEMBURGO'),
    # ('(2)', 'CGDIFRPP', 'CAIXA GERAL DEPÓSITOS PARIS'),
    # ('(2)', 'BESCLULL', 'BES – LUXEMBURGO'),
    # ('(2)', 'BRASATWW', 'BANCO DO BRASIL AG – VIENA'),
    # ('(2)', 'BRASESMM', 'BANCO DO BRASIL AG – SUCURSAL EM ESPANHA'),
    # ('(2)', 'BRASFRPP', 'BANCO DO BRASIL AG – SUCURSAL EM FRANÇA'),
    # ('(2)', 'BRASITMM', 'BANCO DO BRASIL,SA – MILÃO'),
}


def is_iban_bic(iban, bic):
    """verify iban/bic concordance, assumes iban and bic are prevalidated"""
    iban = iban.replace(' ', '')
    bic = bic.replace(' ', '')
    if not iban[:2] == bic[4:6]:
        return False
    if iban[:2] == 'PT':
        account_prefix = iban[4:8]
        if account_prefix in BICS_PT and BICS_PT[account_prefix][0] != bic[:8]:
            return False
    return True


def get_bic_from_iban(iban):
    iban = iban.replace(' ', '')
    account_prefix = iban[4:8]
    if iban[:2] == 'PT' and account_prefix in BICS_PT:
        return BICS_PT[account_prefix][0]
