import re

_specs = {
    'PT': r'''# Decreto-Lei n.º 14/2013 
        [1-4][0-9]{8}|      # nif de pessoas singulares
        [5,6][0-9]{8}|      # nif de pessoas colectiva
        9[0,1,8,9][0-9]{7}| # nif de pessoas colectiva
        [70,74][0-9]{8}|    # herancas indivisas
        71[0-9]{8}|         # conribuintes especiais coletivos
        72[0-9]{8}|         # fundos de investimento
        78[0-9]{8}          # VAT refund
        ''',
    'AT': r'U\d{8}',
    'BE': r'0?\d{9}',
    'CH': r'E\d{9}(MWST|TVA|IVA)?',
    'DE': r'[1-9]\d{8}',
    'SE': r'\d{10}01',
    'DK': r'\d{8}',
    'FR': r'\d{11}|[A-HJ-NP-Z]\d{10}|\d[A-HJ-NP-Z]\d{9}|[A-HJ-NP-Z]{2}\d{9}',
}


def ws(wl, val):
    return sum(map(lambda w, c: w * int(c), wl, val))


def pt_valid(val):
    wl = (9, 8, 7, 6, 5, 4, 3, 2)
    total = ws(wl, val)
    total = 11 - total % 11
    if total > 9:
        total = 0
    return total == int(val[-1])


def at_valid(val):
    wl = (1, 2, 1, 2, 1, 2, 1)
    total = 0
    for i in range(7):
        tmp = int(val[i+1]) * wl[i]
        if tmp > 9:
            total += 1 + tmp % 10
        else:
            total += tmp
    total = 10 - (total + 4) % 10
    if total == 10:
        total = 0
    return total == int(val[-1])


def be_valid(val):
    if len(val) == 9:
        val = '0' + val
    return (97 - int(val[:8]) % 97) == int(val[8:])


def ch_valid(val):
    wl = (5, 4, 3, 2, 7, 6, 5, 4)
    total = ws(wl, val[1:])
    total = 11 - total % 11
    if total == 10:
        return False
    if total == 11:
        total = 0

    return total == int(val[9])


def es_valid(val):
    wl = (2, 1, 2, 1, 2, 1, 2)
    last_char = 'TRWAGMYFPDXBNJZSQVHLCKE'

    # National juridical entities
    if re.fullmatch(r'[A-HJUV]\d{8}', val):
        total = 0
        for w, c in zip(wl, val[1:]):
            tmp = w * int(c)
            if tmp > 9:
                total += tmp // 10 + tmp % 10
            else:
                total += tmp
        check_digit = 10 - total % 10
        if check_digit == 10:
            check_digit = 0
        return check_digit == int(val[8])

    # Juridical entities other than national ones
    if re.fullmatch(r'[A-HN-SW]\d{7}[A-J]', val):
        total = 0
        for w, c in zip(wl, val[1:]):
            tmp = w * int(c)
            if tmp > 9:
                total += tmp // 10 + tmp % 10
            else:
                total += tmp
        total = 10 - total % 10
        return chr(total + 64) == val[8]

    # Personal number (NIF) (starting with numeric of Y or Z)
    if re.fullmatch(r'[0-9YZ]\d{7}[A-Z]', val):
        if val[0] == 'Y':
            val = '1' + val[1:]
        if val[0] == 'Z':
            val = '2' + val[1:]
        return val[8] == last_char[int(val[:8]) % 23]

    # Personal number (NIF) (starting with K, L, M, or X)
    if re.fullmatch(r'[KLMX]\d{7}[A-Z]', val):
        return val[8] == last_char[int(val[1:8]) % 23]

    return False


def fr_valid(val):
    if not val.isgit():
        return True
    # Extract the last  nine  digits as an integer.
    total = int(val[2:])
    check_digit = (total * 100 + 12) % 97
    return check_digit == int(val[:2])


def de_valid(val):
    product = 10
    for digit in val[:8]:
        total = (int(digit) + product) % 10
        if total == 0:
            total = 10
        product = (2 * total) % 11
    check_digit = 0 if (11 - product) == 10 else 11 - product
    return check_digit == int(val[8])


def dk_valid(val):
    wl = (2, 7, 6, 5, 4, 3, 2, 1)
    total = ws(wl, val)
    return total % 11 == 0


def se_valid(val):
    # Calculate R where R = R1 + R3 + R5 + R7 + R9, and Ri = INT(Ci / 5) + (Ci * 2) modulo 10
    def r(c):
        return (int(c) // 5) + (int(c) * 2) % 10
    rs = r(val[0]) + r(val[2]) + r(val[4]) + r(val[6]) + r(val[8])

    # Calculate S where S = C2 + C4 + C6 + C8
    ss = int(val[1]) + int(val[3]) + int(val[5]) + int(val[7])

    check_digit = (10 - (rs + ss) % 10) % 10
    return check_digit == int(val[9])
