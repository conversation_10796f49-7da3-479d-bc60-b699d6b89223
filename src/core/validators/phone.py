import re

phone_specs = {
    '+351': r'''2[0-9]{8}| # numeros fixos
                9[1236][0-9]{7}|  # redes moveis
                30[0-9]{7} # voip
                ''',
}


def is_phone(val):
    val = val.replace(' ', '')
    if val[0] != '+':
        val = '+351' + val

    for prefix, pattern in phone_specs.items():
        if val.startswith(prefix):
            val = val[len(prefix):]
            return not (re.fullmatch(pattern, val, re.VERBOSE) is None)

    return val[1:].isdigit()
