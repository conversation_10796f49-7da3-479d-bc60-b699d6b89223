import time

from django.conf import settings
from django.db import connection


class SQLImporter:
    filename = ''
    encoding = 'utf-8'
    tmp_path = settings.FIXTURE_DIRS[0]
    fields = ''
    skip_first_line = True

    def doit(self):
        self.fobj = self.tmp_path / self.filename
        if not self.fobj.exists():
            print(f'file {self.fobj} does not exist!')
            return
        n = self.create_and_import_tmp_table()
        print(self.__class__.__name__, ':',  n)
        self.after_import()
    
    def create_and_import_tmp_table(self):
        delimiter = ',' if ',' in self.fields else ';'
        tbname = f'tmp_{self.fobj.stem}'
        with self.fobj.open('r', encoding=self.encoding) as f:
            if self.skip_first_line:
                f.readline()
            fields = self.fields.strip()
            fields = fields.split(delimiter)
            fields = [f.lower() + ' varchar(4000)' for f in fields]
            fields = ', '.join(fields)
            with connection.cursor() as cursor:
                cursor.execute(f'create temporary table {tbname}({fields});')
                # cursor.copy_expert(f"COPY {tbname} FROM STDIN WITH CSV DELIMITER '{delimiter}'", f)
                with cursor.copy(f"COPY {tbname} FROM STDIN WITH CSV DELIMITER '{delimiter}'") as copy:
                    copy.write(f.read())
                return cursor.rowcount

    def after_import(self):
        pass

    def runsql(self, sql):
        with connection.cursor() as cursor:
            for sql_bit in sql.split(';'):
                sql_bit = sql_bit.strip()
                if sql_bit:
                    print(sql_bit.replace('\n',' ')[:30],'... ', end='')
                    stime = time.time()
                    cursor.execute(sql_bit)
                    print(f'{cursor.rowcount}  ({time.time()-stime:.3f}s)')

