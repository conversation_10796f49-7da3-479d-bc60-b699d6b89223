from core import htmx
from django.http import HttpResponse

class HtmxMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        request.htmx = htmx.HtmxDetails(request)

        response = self.get_response(request)

        if request.htmx:
            if response.status_code == 302:
                redirect = response['location']
                response = HttpResponse('', status=204)
                response.headers["HX-Redirect"] = redirect
            else:
                request.htmx.update_response(response)
        
        return response
