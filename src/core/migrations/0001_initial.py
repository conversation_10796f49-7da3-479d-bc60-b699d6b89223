# Generated by Django 5.0.4 on 2024-04-30 14:41

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BGTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('function', models.Char<PERSON>ield(max_length=100)),
                ('parameters', models.JSONField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('executed', models.DateTimeField(null=True)),
                ('error', models.TextField(null=True)),
            ],
        ),
    ]
