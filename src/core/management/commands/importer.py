from django.core.management.base import BaseCommand
from django.utils.module_loading import autodiscover_modules

from core.importer import SQLImporter

class Command(BaseCommand):
    help = """
    Import data from fixtures according to classes defined in each app module importer.py
    """

    def handle(self, *args, **options):
        autodiscover_modules("importer")
        for cls in SQLImporter.__subclasses__():
            cls().doit()
