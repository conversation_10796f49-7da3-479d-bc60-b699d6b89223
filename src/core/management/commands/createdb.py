from django.core.management.base import BaseCommand, CommandError
from django.conf import settings


class Command(BaseCommand):
    help = """
    Generates the SQL to create your database for you, as specified in settings.py.
    The envisioned use case is something like this:
        ./manage.py createdatabase [--router=<routername>] | psql -U <db_administrator> -W
    """

    def add_arguments(self, parser):
        parser.add_argument('--router',
                            action='store',
                            dest='router',
                            default='default',
                            help='Use this router-database other then defined in settings.py')
        parser.add_argument('--drop',
                            action='store_true',
                            dest='drop',
                            default=False,
                            help='If given, includes commands to drop any existing user and database.')

    def handle(self, *args, **options):
        router = options.get('router')
        dbinfo = settings.DATABASES.get(router)
        if dbinfo is None:
            raise CommandError("Unknown database router %s".format(router))

        engine = dbinfo.get('ENGINE').split('.')[-1]
        dbuser = dbinfo.get('USER')
        dbpass = dbinfo.get('PASSWORD')
        dbname = dbinfo.get('NAME')

        if engine != 'postgresql':
            raise CommandError(f"Designed only for postgresql (router {router})")

        if options.get('drop'):
            print(f"DROP DATABASE IF EXISTS {dbname};")
            print(f"DROP USER IF EXISTS {dbuser};")

        print(f"CREATE USER {dbuser} WITH ENCRYPTED PASSWORD '{dbpass}' CREATEDB;")
        print(f"CREATE DATABASE {dbname} WITH ENCODING 'UTF-8' OWNER \"{dbuser}\";")
        print(f"GRANT ALL PRIVILEGES ON DATABASE {dbname} TO {dbuser};")
        print(f"\\c {dbname}")
        print(f"CREATE EXTENSION IF NOT EXISTS pg_trgm;")
        print(f"CREATE EXTENSION IF NOT EXISTS unaccent;")
    