import time
import multiprocessing

from django.utils import timezone
from django.core.management.base import BaseCommand
from django.utils.module_loading import import_string

from core.models import BGTask



def worker_process(task_queue):
    while True:
        try:
            task_id = task_queue.get(timeout=10)  # Wait for a task or timeout after 10 seconds
            task = BGTask.objects.get(pk=task_id)
            func = import_string(task.function)
            parameters = task.parameters
            args = parameters.get('args', [])
            kwargs = parameters.get('kwargs', {})
            try:
                func.original(*args, **kwargs)
            except Exception as e:
                task.error = str(e)
            task.status = BGTask.COMPLETED
            task.save()     
        except multiprocessing.queues.Empty:
            pass # No tasks available, continue waiting...
        except KeyboardInterrupt:
            break

class Command(BaseCommand):
    help = """Worker for tasks"""

    def add_arguments(self, parser):
        parser.add_argument('--workers',
                            type=int,
                            dest='workers',
                            default=3,
                            help='number of workers to run background tasks.')

    def handle(self, *args, **options):
        task_queue = multiprocessing.Queue()
        for i in range(options.get('workers')):
            worker = multiprocessing.Process(target=worker_process, args=(task_queue,))
            worker.start()

        # if task worker was stopped and there are IN_PROGRESS tasks re-queue them
        for task in BGTask.objects.filter(status=BGTask.IN_PROGRESS):
            task_queue.put(task.id)

        while True:
            try:
                for task in BGTask.objects.filter(status=BGTask.PENDING):
                    task.status = BGTask.IN_PROGRESS
                    task.save()
                    task_queue.put(task.id)
                time.sleep(10)
            except KeyboardInterrupt:
                break
