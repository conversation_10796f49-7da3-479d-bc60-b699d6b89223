import logging
import time
from datetime import datetime

from django.core.management.base import BaseCommand
from django.utils.module_loading import autodiscover_modules


from core.cron import cronjob_registry

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = """Worker for tasks with calendar scheduled events"""

    def add_arguments(self, parser):
        parser.add_argument('--interactive',
                            action='store_true',
                            dest='interactive',
                            default=False,
                            help='allows to run a cron job interactively.')

    def handle(self, *args, **options):
        autodiscover_modules("process")
        if options.get('interactive'):
            self.interactive()
        else:
            self.cron_worker()

    def interactive(self):
        for i, cron in enumerate(cronjob_registry):
            print(f'{i+1} - {cron.name} (due run: {cron.next_run})')
        n = input('\nnumber to execute: ')
        cronjob_registry[int(n)-1].func()

    def cron_worker(self):
        try:
            logger.debug('cron worker started')

            for cron in cronjob_registry:
                logger.debug(f'** cron job: {cron.name}, {cron.calendar_exp} <{cron.func.__module__}.{cron.func.__name__}>')

            while True:
                for cron in cronjob_registry:
                    if cron.next_run < datetime.now():
                        try:
                            # log execution
                            logger.debug(f'running {cron.name} due_run: {cron.next_run}')
                            cron.func()
                        except Exception as e:
                            #log exception str(e)
                            logger.error(str(e))
                        # set next_run
                        cron.update_next_run()
                        # logger.debug(f'{cron.name} next_run: {cron.next_run}')
                time.sleep(10)
        except KeyboardInterrupt:
            logger.debug('cron worker ended')


