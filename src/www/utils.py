"""
parse url for user, password, host, port, path and optional params via query string, view examples

    postgres://user_name:user_pass@localhost:5432/dbname  
    email://user_name:<EMAIL>:587?use_tls=true
    redis://127.0.0.1:6379  

"""

from urllib.parse import urlparse, parse_qs


class UrlSettings():
    def __init__(self, url):
        pr = urlparse(url)
        for prop in ['scheme', 'username', 'password', 'hostname', 'port', 'path']:
            setattr(self, prop, getattr(pr, prop))

        if pr.query:
            for k, v in parse_qs(pr.query).items():
                val = v[0]
                if val.isdigit():
                    val = int(val)
                elif val.lower() in ['true', 'false']:
                    val = val.lower() == 'true'
                setattr(self, k.lower(), val)

    def __getattr__(self, item):
        return None