"""
URL configuration for www project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
import importlib
import os

from django.contrib import admin
from django.urls import path, include
from django.apps import apps
from django.conf import settings
from django.http import HttpResponse

def no_reponse(request):
    return HttpResponse(status=204)


urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('core.urls')),
    path('favicon.ico', no_reponse),

    path('noapp/', include('noapp.urls')),
]



# include urls from apps that are installed in this project
for app in ['empresa', 'terceiro', 'condo']:
    if app not in apps.app_configs:
        continue
    try:
        # Try to import the app's urls.py
        app_urls = importlib.import_module(f'{app}.urls')
        urlpatterns += [
            path(f'{app}/', include(f'{app}.urls')),
        ]
    except Exception as e:
        print(e)
        pass
    except ModuleNotFoundError:
        # If the app doesn't have a urls.py, skip it
        print(app,'  no urls')
        pass


# media files handling

def nginx_serve_media(request, path):
    response = HttpResponse()
    if request.user.is_authenticated:
        del response.headers['Content-Type']
        response.headers['X-Accel-Redirect'] = f'/pmedia/{path}'
    return response

if settings.DEBUG:
    from django.views.static import serve

    urlpatterns += [
       path(settings.MEDIA_URL.lstrip('/')+'<path:path>', serve, {'document_root': settings.MEDIA_ROOT})
    ]
else:
    urlpatterns += [
       path(settings.MEDIA_URL.lstrip('/')+'<path:path>', nginx_serve_media)
    ]
