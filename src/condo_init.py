#!/usr/bin/env python

import os
os.remove('condo.sqlite3')


import django

django.setup()



from django.core.management import call_command

call_command('migrate')

#from empresa.models import Empresa
from terceiro.models import <PERSON>rc<PERSON>
from condo.models import Condo, Fraca<PERSON>, Orcamento, OrcamentoLinha, <PERSON><PERSON><PERSON>

from core.validators.iban import get_bic_from_iban



e = Condo(nome='Condomínio das Laranjeiras', morada='Rua António Arújo nº 77', cpostal='4580-045 Paredes', nif='901491950', iban='*************************')
e.bic = get_bic_from_iban(e.iban)
e.save()

lst = [
    Terceiro(tipo='P', nome='Adão Francisco More<PERSON> Oliveira', email='<EMAIL>', telefone='917088808', morada='Rua António Araújo nº 77 - 1º Esq.Frt.', cpostal_alt='4580-045 Paredes', empresa=e),
    <PERSON><PERSON><PERSON>(tipo='P', nome='Ant<PERSON><PERSON> Teixeira da Rua', email='<EMAIL>', telefone='912687323', morada='Rua António Arújo nº 77, 4ºesq', cpostal_alt='4580-045 Paredes', nif='190502762', empresa=e),
    Terceiro(tipo='P', nome='Carlos Marcelino de Oliveira Marques da Silva', email='<EMAIL>', telefone='912.392.802', morada='Rua António Araújo nº 77 - 2º Drt.Tras', cpostal_alt='4580-045 Paredes', empresa=e),
    Terceiro(tipo='P', nome='Celeste de Barros Lopes', email='<EMAIL>', telefone='917.644.183', morada='Rua António Araújo nº 77 - 1ºDrt.Tras', cpostal_alt='4580-045 Paredes', nif='216527350', empresa=e),
    Terceiro(tipo='F', nome='ENDESA Energia S.A. - Sucursal Portugal', email='<EMAIL>', telefone='800101033', morada='Quinta da Fonte, Edifício D. Manuel I, Piso 0, Ala B', cpostal_alt='2770-203 Paço de Arcos', nif='980245974', empresa=e),
    Terceiro(tipo='F', nome='Extinvale - Segurança Contra Incêndios, Lda', email='<EMAIL>', telefone='255813991/2', morada='Rua do Cruzeiro no 478 Nespereira', cpostal_alt='4620-404 NESPEREIRA LSD', nif='505898977', iban='************************', empresa=e),
    Terceiro(tipo='P', nome='IMORUBI Imobiliária S.A.', email='<EMAIL>', telefone='967052771', morada='Rua António Araújo nº 81', cpostal_alt='4580-045 Paredes', nif='504413201', iban='.', empresa=e),
    Terceiro(tipo='P', nome='Imelda Maria Correia de Oliveira Amorim', email='<EMAIL>', telefone='917096592', morada='Rua de Carreiro de lama, nº 18 - 4ºB', cpostal_alt='4580-063 Paredes', nif='155569570', empresa=e),
    Terceiro(tipo='P', nome='Isabel Maria Correia de Oliveira', email='<EMAIL>', telefone='962950175', morada='Rua Infante D. Henrique nº 79 -  2º Drt.', cpostal_alt='4580-111 Paredes', empresa=e),
    Terceiro(tipo='F', nome='JARDINAGEM António Silva', email='<EMAIL>', telefone='936593676', morada='.', cpostal_alt='.', nif='.', iban='.', empresa=e),
    Terceiro(tipo='P', nome='Joaquim Augusto Moreira da Silva', email='<EMAIL>', telefone='917644183', morada='Rua António Araújo nº 77 - 1º Drt.Frt.', cpostal_alt='4580-045 Paredes', nif='184848555', empresa=e),
    Terceiro(tipo='P', nome='José Fernando Nogueira Rosendo', email='<EMAIL>', telefone='919578818', morada='Rua António Araújo, nº 77 - 3º Drt.Frt.', cpostal_alt='4580-045 Paredes', empresa=e),
    Terceiro(tipo='F', nome='MEO - Serviços de Comunicações e Multimédia, S.A.', email='<EMAIL>', telefone='.', morada='Av. Fontes Pereira de Melo, 40', cpostal_alt='1069-300 Lisboa', nif='504615947', empresa=e),
    Terceiro(tipo='P', nome='Maria de Fátima Coelho Ferreira da Mota', email='<EMAIL>', telefone='918.959.976', morada='Rua António Araújo nº 77 - 1º Esq.Tras', cpostal_alt='4580-045 Paredes', empresa=e),
    Terceiro(tipo='F', nome='Montepio', email='<EMAIL>', empresa=e),
    Terceiro(tipo='P', nome='Mutuotriplo Imobiliaria, Lda', email='<EMAIL>', telefone='966.055.435', morada='Travessa da Rua das Bichas nº 163', cpostal_alt='4620-213 Lodares', nif='510790925', empresa=e),
    Terceiro(tipo='F', nome='ORONA PORTUGAL, UNIPESSOAL, LDA', email='<EMAIL>', telefone='227 157 410', morada='PQ BELOURA OFFICE PARK, RUA CENTRO EMPRESARIAL EDIF.8, PISO 1', cpostal_alt='2710-444 - SINTRA', nif='501606319', empresa=e),
    Terceiro(tipo='P', nome='Papelaria AGUARELA', email='<EMAIL>', telefone='255783925 / 965517000', morada='Rua Doutor José Bragança Tavares nº 249 - 1ºEsq.', cpostal_alt='4580-593 Mouriz', nif='192296230', empresa=e),
    Terceiro(tipo='F', nome='ParedesGest', email='<EMAIL>', telefone='255 777 334', morada='Rua Doutor José Bragança Tavares, no 63, Loja G', cpostal_alt='4580-124 Paredes', empresa=e),
    Terceiro(tipo='P', nome='Patrícia Maria Ferreira Alves', email='<EMAIL>', telefone='912146572', morada='Rua António Araújo nº 77 - 3º Esq.Tras', cpostal_alt='4580-045 Paredes', nif='226322017', empresa=e),
    Terceiro(tipo='F', nome='SMAS PAREDES Água e Saneamento', email='<EMAIL>', telefone='.', morada='Rua de Timor nº27', cpostal_alt='4580-015 Paredes', nif='600087689', empresa=e),
    Terceiro(tipo='P', nome='SurpriseHouse - Investments Lda', email='<EMAIL>', telefone='918110448', morada='Rua do Capelo nº 27', cpostal_alt='4585-370 Rebordosa', nif='503539910', empresa=e),
    Terceiro(tipo='F', nome='LIMPEZA Deolinda Silva', email='<EMAIL>', iban='*************************', bic='TOTAPTPL'),
    Terceiro(tipo='F', nome='Eni PLenitude Iberia SL', morada='Edificio Prime, Avenida da quinta Grande 53, 8ºA', cpostal_alt='2620-156 Alfragide', nif='980575931', empresa=e),
    Terceiro(tipo='F', nome='Dra Sandra Teixeira Gomes', telefone='917640594', empresa=e),
    Terceiro(tipo='F', nome='Dra Manuela Reis', telefone='919050093', empresa=e),
    Terceiro(tipo='F', nome='Bruno Ricardo Mendes Ferreira, Unipessoal Lda.', email="<EMAIL>", morada="Rua de Samil, nº555", telefone='916227970', cpostal_alt="4560-034 Abragão", nif="514994754", empresa=e),
]
Terceiro.objects.bulk_create(lst)


lst = [
    Fracao(condo=e, nome_abrev='A', nome='4º Recuado Drt.', proprietario=Terceiro.objects.get(nome="IMORUBI Imobiliária S.A."), permilagem='8314'),
    Fracao(condo=e, nome_abrev='B', nome='4º Recuado Esq.', proprietario=Terceiro.objects.get(nome="António Alexandre da Cunha Monteiro Teixeira da Rua"), permilagem='8050'),
    Fracao(condo=e, nome_abrev='C', nome='3º Drt. Frt', proprietario=Terceiro.objects.get(nome="José Fernando Nogueira Rosendo"), permilagem='5395'),
    Fracao(condo=e, nome_abrev='D', nome='3º Esq. Frt', proprietario=Terceiro.objects.get(nome="IMORUBI Imobiliária S.A."), permilagem='5306'),
    Fracao(condo=e, nome_abrev='E', nome='3º Esq. Tras', proprietario=Terceiro.objects.get(nome="Patrícia Maria Ferreira Alves"), permilagem='5007'),
    Fracao(condo=e, nome_abrev='F', nome='3º Drt. Tras', proprietario=Terceiro.objects.get(nome="IMORUBI Imobiliária S.A."), permilagem='4984'),
    Fracao(condo=e, nome_abrev='G', nome='2º Drt. Frt', proprietario=Terceiro.objects.get(nome="Isabel Maria Correia de Oliveira"), permilagem='5236'),
    Fracao(condo=e, nome_abrev='H', nome='2º Esq. Frt', proprietario=Terceiro.objects.get(nome="Carlos Marcelino de Oliveira Marques da Silva"), permilagem='5195'),
    Fracao(condo=e, nome_abrev='I', nome='2º Esq. Tras', proprietario=Terceiro.objects.get(nome="Imelda Maria Correia de Oliveira Amorim"), permilagem='4885'),
    Fracao(condo=e, nome_abrev='J', nome='2º Drt. Tras', proprietario=Terceiro.objects.get(nome="SurpriseHouse - Investments Lda"), permilagem='5190'),
    Fracao(condo=e, nome_abrev='K', nome='1º Drt. Frt', proprietario=Terceiro.objects.get(nome="Joaquim Augusto Moreira da Silva"), permilagem='5192'),
    Fracao(condo=e, nome_abrev='L', nome='1º Esq. Frt', proprietario=Terceiro.objects.get(nome="Adão Francisco Moreira de Oliveira"), permilagem='4954'),
    Fracao(condo=e, nome_abrev='M', nome='1º Esq. Tras', proprietario=Terceiro.objects.get(nome="Maria de Fátima Coelho Ferreira da Mota"), permilagem='4634'),
    Fracao(condo=e, nome_abrev='N', nome='1º Drt. Tras', proprietario=Terceiro.objects.get(nome="Celeste de Barros Lopes"), permilagem='5022'),
    Fracao(condo=e, nome_abrev='O', nome='R/C Nº83', proprietario=Terceiro.objects.get(nome="Papelaria AGUARELA"), permilagem='4387'),
    Fracao(condo=e, nome_abrev='P', nome='R/C Nº81', proprietario=Terceiro.objects.get(nome="IMORUBI Imobiliária S.A."), permilagem='7146'),
    Fracao(condo=e, nome_abrev='Q', nome='R/C Nº75', proprietario=Terceiro.objects.get(nome="Mutuotriplo Imobiliaria, Lda"), permilagem='10332'),
    Fracao(condo=e, nome_abrev='R', nome='Garagem R', proprietario=Terceiro.objects.get(nome="IMORUBI Imobiliária S.A."), permilagem='509'),
    Fracao(condo=e, nome_abrev='S', nome='Garagem S', proprietario=Terceiro.objects.get(nome="IMORUBI Imobiliária S.A."), permilagem='262'),
]
Fracao.objects.bulk_create(lst)


orcamento = Orcamento(condo=e, ano='2024', fcr='20')
orcamento.save()


lst = [
    OrcamentoLinha(orcamento=orcamento, rubrica='Àgua', valor='300.00'),
    OrcamentoLinha(orcamento=orcamento, rubrica='Despesas Bancárias', valor='100.00'),
    OrcamentoLinha(orcamento=orcamento, rubrica='Eletricidade', valor='1500.00'),
    OrcamentoLinha(orcamento=orcamento, rubrica='Elevadores', valor='1500.00'),
    OrcamentoLinha(orcamento=orcamento, rubrica='ElevadoresPT', valor='250.00'),
    OrcamentoLinha(orcamento=orcamento, rubrica='Extintores', valor='100.00'),
    OrcamentoLinha(orcamento=orcamento, rubrica='Jardim', valor='720.00'),
    OrcamentoLinha(orcamento=orcamento, rubrica='Limpeza', valor='1200.00'),
    OrcamentoLinha(orcamento=orcamento, rubrica='Manutenção geral', valor='500.00'),
    OrcamentoLinha(orcamento=orcamento, rubrica='Expediente', valor='100.00'),
    OrcamentoLinha(orcamento=orcamento, rubrica='Outros', valor='500.00'),
]
OrcamentoLinha.objects.bulk_create(lst)


lst = [
    Receita(tipo='O', fracao=Fracao.objects.get(nome="4º Recuado Drt."), data='2023-12-31', descricao='Valor em atraso 2023', valor='249.42', valor_por_pagar='249.42'),
    Receita(tipo='O', fracao=Fracao.objects.get(nome="3º Esq. Frt"), data='2023-12-31', descricao='Valor em atraso 2023', valor='164.32', valor_por_pagar='164.32'),
    Receita(tipo='O', fracao=Fracao.objects.get(nome="3º Drt. Tras"), data='2023-12-31', descricao='Valor em atraso 2023', valor='174.40', valor_por_pagar='174.40'),
    Receita(tipo='O', fracao=Fracao.objects.get(nome="R/C Nº81"), data='2023-12-31', descricao='Valor em atraso 2023', valor='250.05', valor_por_pagar='250.05'),
    Receita(tipo='O', fracao=Fracao.objects.get(nome="Garagem R"), data='2023-12-31', descricao='Valor em atraso 2023', valor='17.81', valor_por_pagar='17.81'),
    Receita(tipo='O', fracao=Fracao.objects.get(nome="Garagem S"), data='2023-12-31', descricao='Valor em atraso 2023', valor='9.17', valor_por_pagar='9.17'),
    Receita(tipo='O', fracao=Fracao.objects.get(nome="3º Esq. Tras"), data='2023-12-31', descricao='Valor em atraso 2023', valor='2244.79', valor_por_pagar='2244.79'),
    Receita(tipo='O', fracao=Fracao.objects.get(nome="R/C Nº75"), data='2023-12-31', descricao='Valor em atraso 2023', valor='1354.77', valor_por_pagar='1354.77'),
]
Receita.objects.bulk_create(lst)


from condo.process import aplicar_orcamento

aplicar_orcamento(e, 2024)