 SELECT dcf.id,
    dcf.codigo,
    dcf.tipo,
    dcf.nome,
    d.nome AS distrito,
    c.nome AS concelho,
    f.nome AS freguesia
   FROM common_dcf dcf
     LEFT JOIN common_dcf d ON "substring"(dcf.codigo::text, 1, 2) = d.codigo::text
     LEFT JOIN common_dcf c ON length(c.codigo::text) >= 4 AND "substring"(dcf.codigo::text, 1, 4) = c.codigo::text
     LEFT JOIN common_dcf f ON length(f.codigo::text) >= 6 AND "substring"(dcf.codigo::text, 1, 6) = f.codigo::text;



match q.split("/"):
                case [distrito, concelho, freguesia]:
                    qs = self.get_queryset().raw("select id, concat_ws(' / ', distrito,concelho,freguesia) as nome "
                                                 "from common_v_dcf "
                                                 "where distrito ilike %s and concelho ilike %s and freguesia ilike %s",
                                                 ['%'+distrito+'%', '%'+concelho+'%', '%'+freguesia+'%'])
                case [concelho, freguesia]:
                    qs = self.get_queryset().raw("select id, concat_ws(' / ', distrito,concelho,freguesia) as nome "
                                                 "from common_v_dcf "
                                                 "where concelho ilike %s and freguesia ilike %s",
                                                 ['%'+concelho+'%', '%'+freguesia+'%'])
                case [freguesia]:
            

