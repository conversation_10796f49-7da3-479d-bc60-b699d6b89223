# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore


src/www/settings/scholr.py
src/www/settings/scholrapp.py
src/www/settings/condoapp.py
src/www/settings/gptapp.py

venv/
fixtures/

logs/*
#just to copy dir
!logs/.gitkeep  

src/media_condo/
src/media_scholr/

# Compiled Python bytecode
*.py[cod]

# Log files
*.log
logs_condo/
logs_scholr/

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# local databases
src/core.db
*.sqlite3
.qodo
