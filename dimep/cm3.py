
import socket

START = 0xF8
STOP = 0xF0


class CM3Message:
    FUNCAO = 0x0
    NUMERO_MSG = -1
    def __init__(self, data):
        self.data = data
        self.num_msg = None

    def send(self, s):
        msg = bytearray()
        msg.append(START)
        msg.append(self.FUNCAO)
        if self.num_msg is None:
            CM3Message.NUMERO_MSG += 1
            if CM3Message.NUMERO_MSG > 255:
                CM3Message.NUMERO_MSG = 1
            self.num_msg = CM3Message.NUMERO_MSG 
        msg.append(self.num_msg.to_bytes(1))
        msg.extend(len(self.dados).to_bytes(2, byteorder='big'))
        msg.extend(self.dados)
        check_sum = msg[1]
        for b in msg[2:]:
            check_sum ^= b
        msg.append(check_sum)  # checksum
        msg.append(STOP)  # stop
        s.sendall(msg)        

    @classmethod
    def build_from_socket(cls, s):
        start, func, num_msg, s1, s2  = s.recv(5)
        # assert start = START
        obj = None
        for mc in CM3Message.__subclasses__():
            if func == mc.FUNCAO:
                obj = mc()
                break
        if obj is None:
            obj = cls()
        obj.FUNCAO = func
        obj.num_msg = num_msg
        size = s1 * 256 + s2
        if size:
            mc.data = s.recv(size)
        check_sum, stop = s.recv(2)
        # verify checksum
        
        # assert stop = STOP

        return obj
    

class CM3Message_OK(CM3Message):
    FUNCAO = 0x0

class CM3Message_Mensagem_Invalida(CM3Message):
    FUNCAO = 0x1

class CM3Message_Status(CM3Message):
    FUNCAO = 0x20

class CM3Message_Status_Operacao(CM3Message):
    FUNCAO = 0x20