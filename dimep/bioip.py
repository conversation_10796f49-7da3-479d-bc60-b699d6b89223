

def bcd_str2bytes(ss):
    return int(ss, 16).to_bytes(len(ss)//2, byteorder='big')

def bcd_bytes2str(bb):
    return '%x' % int.from_bytes(bb)


class Field:
    def __init__(self, size=1):
        self.size = size
        self.offset = 0

    def __set_name__(self, owner, name): 
        self.storage_name = name         

    def __set__(self, instance, value):
        instance.__dict__[self.storage_name] = value

    def __get__(self, instance, owner):
        return instance.__dict__[self.storage_name]

    def from_bytes(self, instance, data):
        self.__set__(instance, data[self.offset: self.offset+self.size])

    def to_bytes(self, instance):
        return instance.__dict__[self.storage_name]


class FieldInt(Field):
    def from_bytes(self, instance, data):
        self.__set__(instance, int.from_bytes(data[self.offset: self.offset+self.size], byteorder='big'))

    def to_bytes(self, instance):
        return int(instance.__dict__[self.storage_name]).to_bytes(self.size, byteorder='big')


class FieldBool(Field):
    def __init__(self):
        super().__init__()

    def from_bytes(self, instance, data):
        self.__set__(instance, True if data[self.offset: self.offset+self.size]==b'\x01' else False)

    def to_bytes(self, instance):
        return b'\x01' if instance.__dict__[self.storage_name] else b'x00'


class FieldBCD(Field):
    def from_bytes(self, instance, data):
        bin_data = data[self.offset: self.offset+self.size]
        self.__set__(instance, '%x' % int.from_bytes(bin_data) )

    def to_bytes(self, instance):
        return int(instance.__dict__[self.storage_name], 16).to_bytes(self.size, byteorder='big') 


class MC(type):
    def __new__(meta_cls, cls_name, bases, cls_dict):
        fields = []
        offset = 0
        for name, val in cls_dict.items():
            # print(name, val)
            if issubclass(type(val), Field):
                val.offset = offset
                offset += val.size
                fields.append(val)
        cls_dict['fields'] = fields
        return super().__new__(meta_cls, cls_name, bases, cls_dict)
    

class BioMessage(metaclass=MC):
    FUNCAO = 0x00
    START = 0xFE
    STOP = 0xF0
    num_msg = 0

    def __init__(self) -> None:
        self.num_msg = 0
        self.data = b''
        self.endereco = 0

    def build_data(self):
        data = bytearray()
        for f in self.fields:
            # print(f)
            data.extend(f.to_bytes(self))
        self.data = data

    def send(self, cs):
        msg = bytearray()
        msg.append(BioMessage.START)
        msg.extend(b'\x00\x01')
        msg.append(self.FUNCAO)
        BioMessage.num_msg += 1
        if BioMessage.num_msg > 255:
            BioMessage.num_msg = 1
        msg.append(BioMessage.num_msg)
        self.build_data()
        msg.extend(len(self.data).to_bytes(2, byteorder='big'))
        msg.extend(self.data)
        check_sum = msg[1]
        for b in msg[2:]:
            check_sum ^= b
        msg.append(check_sum)
        msg.append(BioMessage.STOP)
        print(msg)
        return msg
        # cs.sendall(msg)

    @classmethod
    def build_from_socket(cls, cs):
        start, e1, e2, func, num_msg, s1, s2  = cs.read(7)
        print(start,e1,e2,func,num_msg,s1,s2)
        obj = None
        for mc in BioMessage.__subclasses__():
            if func == mc.FUNCAO:
                obj = mc()
                break
        if obj is None:
            obj = cls()
        obj.FUNCAO = func
        obj.num_msg = num_msg
        size = s1 * 256 + s2
        if size:
            obj.data = cs.read(size)
        check_sum, stop = cs.read(2)
        # verify checksum
        
        # assert stop = STOP

        # build fields from data
        for f in obj.fields:
            f.from_bytes(obj, obj.data)
        
        return obj


#
# Mensagens de iniciativa do equipamento
#

class BioMessage_mensagem_vazia(BioMessage):
    FUNCAO = 0x40
    tem_dados_batch = FieldBool()
    operacao = FieldInt()
    tem_digital = FieldBool()
    qtd_digital = FieldBCD(2)

class BioMessage_consulta_acesso(BioMessage):
    FUNCAO = 0x41
    codigo = FieldBCD(8)
    tipo_da_consulta = FieldInt()
    sentido_da_consulta = FieldInt()
    funcao_da_consulta = FieldInt()
    numero_dedo = FieldInt()
    data_hora = FieldBCD(6)
    tamanho_template = FieldInt()
    template = Field(1024)

class BioMessage_fim_acesso(BioMessage):
    FUNCAO = 0x42
    codigo = FieldBCD(8)
    evento = FieldInt()
    funcao = FieldInt()
    data_hora = FieldBCD(6)

class BioMessage_coleta_registro_batch(BioMessage):
    FUNCAO = 0x43
    numero_bloco = FieldBCD()
    codigo = FieldBCD(8)
    data = FieldBCD(3)
    hora = FieldBCD(2)
    evento = FieldInt()
    funcao = FieldInt()
    segundo = FieldBCD()

class BioMessage_consulta_por_template(BioMessage):
    FUNCAO = 0x45
    tipo_da_consulta = FieldInt()
    sentido_da_consulta = FieldInt()
    funcao_da_consulta = FieldInt()
    data_hora = FieldBCD(6)
    tamanho_template = FieldInt()
    template = Field(1024)




m1 = BioMessage_mensagem_vazia()
# print(m1.fields)
m1.tem_dados_batch = True
m1.operacao = 1
m1.tem_digital = True
m1.qtd_digital = '0130'

import io

a=''
stream1 = io.BytesIO(m1.send(a))
stream2 = io.BytesIO(m1.send(a))


obj = BioMessage.build_from_socket(stream2)

print(obj)
print(obj.tem_dados_batch, m1.operacao, m1.tem_digital, m1.qtd_digital)