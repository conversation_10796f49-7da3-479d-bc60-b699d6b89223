import socket
import selectors

START = 0xFE
STOP = 0xF0


def bcd_to_int(n):
    return int(('%x' % n), base=10)


def int_to_bcd(n):
    return int(str(n), base=16)


class Field:
    def __init__(self, typ='byte', length=1) -> None:
        self.typ = typ
        self.length = length

    def to_bytes(self):
        if self.typ == 'byte':
            pass
        elif self.typ == 'bool':
            pass
        elif self.typ == 'bcd':
            pass

    def from_bytes(self, data):
        pass
        


class BioMessage:
    FUNCAO = 0

    def __init__(self) -> None:
        self.endereco = None
        self.dados = None
        self.num_msg = None


    def send(self, s):
        msg = bytearray()
        msg.append(START)

        s.sendall(msg)


    @classmethod
    def build_from_socket(cls, s):
        start, endereco, func, num_msg, s1, s2  = s.recv(5)
        # assert start = START
        obj = None
        for mc in BioMessage.__subclasses__():
            if func == mc.FUNCAO:
                obj = mc()
                break
        if obj is None:
            obj = cls()
        obj.FUNCAO = func
        obj.num_msg = num_msg
        size = s1 * 256 + s2
        if size:
            obj.data = s.recv(size)
        check_sum, stop = s.recv(2)
        # verify checksum
        
        # assert stop = STOP

        # build fields from data
        if obj.fields:
            data = obj.data
            offset = 0
            for prop, size in obj.fields.items():
                setattr(obj, prop, obj.data[offset:offset+size])
                offset += size

        return obj


#
# Mensagens de iniciativa do equipamento
#

class BioMessage_mensagem_vazia(BioMessage):
    FUNCAO = 0x40
    fields = {
        'tem_dados_batch' : 1,
        'operacao': 1,
        'tem_digital': 1,
        'qtd_digital': 2,
    }

class BioMessage_consulta_acesso(BioMessage):
    FUNCAO = 0x41

class BioMessage_fim_acesso(BioMessage):
    FUNCAO = 0x42

class BioMessage_coleta_registro_batch(BioMessage):
    FUNCAO = 0x43


#
# Resposta do software as mensagens de iniciativa do equipamento
#

class BioMessage_ACK1(BioMessage):
    FUNCAO = 0x20

class BioMessage_ACK2(BioMessage):
    FUNCAO = 0x21

class BioMessage_ACK3(BioMessage):
    FUNCAO = 0x22

class BioMessage_mensagem_de_acesso(BioMessage):
    FUNCAO = 0x23





#
# Resposta do equipamento as mensagens de iniciativa do software    
#

class BioMessage_mensagem_ok(BioMessage):
    FUNCAO = 0x60	

class BioMessage_mensagem_invalida(BioMessage):
    FUNCAO = 0x61

class BioMessage_mensagem_status(BioMessage):
    FUNCAO = 0x62

class BioMessage_mensagem_aguarde(BioMessage):
    FUNCAO = 0x63

class BioMessage_digital_duplicada(BioMessage):
    FUNCAO = 0x64

class BioMessage_digital(BioMessage):
    FUNCAO = 0x71

class BioMessage_status_operacao(BioMessage):
    FUNCAO = 0x30




#
# class for equipment
#

class BIOIP_Terminal:
    def __init__(self, name:str, ip:str) -> None:
        self.name = name
        self.ip = ip
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.setblocking(False)
        self.sock.connect_ex((self.ip, 3000))

    def start(self):
        # send msg and other starting stuff
        pass

    def process(self):
        msg = BioMessage.build_from_socket(self.sock)
        match msg:
            case BioMessage_mensagem_vazia():
                # log
                # send ack
                BioMessage_ACK1().send(self.sock)



# nome=cantina_c1
# numero_relogio=7
# endereco_ip=************
# tempo_timeout=20
# acionamento=13
# tempo_acionamento=10
# liberacao_entrada=0
# liberacao_saida=0
# sentido_giro=1
# tipo_ack=1
# modelo_relogio=3
# tipo_acesso_digital=1


terminals = {
    "Oficina 1": "************",
    "Oficina 2": "************",
    "Oficina 3": "************",
    "Oficina 4": "************",
 
    "Termas 1": "************",
    "Termas 2": "************",

    "cantina 1": "************",
    "cantina 2": "************",
}



sel = selectors.DefaultSelector()

for name, ip in terminals:
    bioip_terminal = BIOIP_Terminal(name, ip)
    sel.register(bioip_terminal.sock, selectors.EVENT_READ, bioip_terminal)


while True:
    print('inhere')
    events = sel.select(timeout=None)
    for key, mask in events:
        terminal = key.data
        terminal.process()

